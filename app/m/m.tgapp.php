<?php

class m_tgapp extends m_base
{
    const DATA_TYPE_NUMBER = 1;//纯数字
    const DATA_TYPE_ALPHA = 2;//英文字母
    const DATA_TYPE_ALPHA_NUM = 3;//字母加数字
    const DATA_TYPE_CHINESE = 4;//只能为中文
    const DATA_TYPE_SELECT = 5;//下拉框
    const DATA_TYPE_PHONE = 6;//手机号
    const DATA_TYPE_HIDE_PHONE = 7;//脱敏手机号
    const DATA_TYPE_URL = 8;//链接地址
    const DATA_DATE_YMD = 9;//日期Y-m-d
    const DATA_TYPE_ALPHA_LOWER = 10;//只能为英文字母小写字母
    const DATA_TYPE_ALPHA_UPPER = 11;//只能为英文字母大写字母
    const DATA_TYPE_ALPHA_LOWER_NUM = 12;//只能为英文字母小写字母加数字
    const DATA_TYPE_ALPHA_UPPER_NUM = 13;//只能为英文字母大写字母加数字
    const DATA_TYPE_SINGLE_NRK = 14;//单选内容库
    const DATA_TYPE_MULTIPLE_NRK = 15;//多选内容库
    const DATA_TYPE_TJ_BATCH = 16;//回填批次

    const TJ_INFO_TYPE_INPUT = 1;
    const TJ_INFO_TYPE_IMAGE = 2;
    const TJ_INFO_TYPE_VIDEO = 3;
    //凭证类型 1.输入框  2.图片 3.视频
    const TJ_INFO_TYPE_ARR = [self::TJ_INFO_TYPE_INPUT, self::TJ_INFO_TYPE_IMAGE, self::TJ_INFO_TYPE_VIDEO];

    //提交凭证数据类型 0 不限制 1、纯数字 2、纯英文 3、英文加数字 4、纯中文 5、下拉框选择 6、手机号 7、脱敏手机号 8、URL地址 9、日期Y-m-d
    const TJ_INFO_DATA_TYPE = [
        0 => '不限制',
        self::DATA_TYPE_NUMBER => '纯数字',
        self::DATA_TYPE_ALPHA => '纯英文（支持大小写字母）',
        self::DATA_TYPE_ALPHA_LOWER => '英文字母（仅小写字母）',
        self::DATA_TYPE_ALPHA_UPPER => '英文字母（仅大小字母）',
        self::DATA_TYPE_ALPHA_LOWER_NUM => '英文与数字（仅小写字母）',
        self::DATA_TYPE_ALPHA_UPPER_NUM => '英文与数字（仅大写字母）',
        self::DATA_TYPE_ALPHA_NUM => '英文与数字（支持大小写）',
        self::DATA_TYPE_CHINESE => '纯中文',
        self::DATA_TYPE_SELECT => '下拉选择框',
        self::DATA_TYPE_PHONE => '手机号',
        self::DATA_TYPE_HIDE_PHONE => '脱敏手机号',
        self::DATA_TYPE_URL => '链接格式',
        self::DATA_DATE_YMD => '日期格式如：2025-01-01',
        self::DATA_TYPE_SINGLE_NRK => '单选内容库',
        self::DATA_TYPE_MULTIPLE_NRK => '多选内容库',
        self::DATA_TYPE_TJ_BATCH => '回填批次',
    ];

    const DATA_FORMAT_TYPE_UPPER = 1;    //数据转大写
    const DATA_FORMAT_TYPE_LOWER = 2;    //数据转小写

    //普通项目
    const TG_APP_LX_NORMAL = 1;
    //推广项目水上
    const TG_APP_LX_WATER = 2;

    const TG_APP_LX_ARR = [
        self::TG_APP_LX_NORMAL,
        self::TG_APP_LX_WATER,
    ];

    /**
     * 获取用户推广的app信息
     * @param $d
     * @param $user
     * @return array
     * @throws Exception
     */
    public function get_u_tg_apps($d, $user)
    {
        $d = glwb($d);
        $user = glwb($user);
        $uid = ints($user['uid']);
        if (!$uid) {
            return rs('抱歉，用户登录已过期');
        }

        $w = ['lx' => m_tgapp::TG_APP_LX_NORMAL, 'status' => 1];

//        if ($user['channel_id']) {
//            $channel = M()->get('x_third_channel', 'id,show_app,hide_app', ['id' => $user['channel_id']]);
//            if (!empty($channel)) {
//                $showApp = array_x(explode(',', $channel['show_app']));
//                $hideApp = array_x(explode(',', $channel['hide_app']));
//                if (!empty($showApp) && !empty($hideApp)) {
//                    $w['id'] = $showApp;
//                    $w['id[!=]'] = $hideApp;
//                } else if (!empty($showApp)) {
//                    $w['id'] = $showApp;
//                } else if (!empty($hideApp)) {
//                    $w['id[!=]'] = $hideApp;
//                }
//            }
//        }

        //如果筛选地推 网推 自动增加全部
        if ($d['dt_type']) {
            $w['dt_types[~]'] = '"' . $d['dt_type'] . '"';
        }

        if ($d['keywords']) {
            $w['name[~]'] = $d['keywords'];
            unset($w['status']);
            $w['OR'] = ['down_is_show' => 1, 'status' => 1];
        }

        if ($d['app_ids']) {
            $d['app_ids'] = inta_x($d['app_ids']);
            if (!empty($d['app_ids'])) {
                $w['id'] = $d['app_ids'];
            }
        }

        //推广人黑名单
        $blackAppIds = M()->select('x_tg_app_black_list', 'appid', ['tg_uid' => $uid]);
        $blackAppIds = array_column($blackAppIds, 'appid');
        if (!empty($blackAppIds)) {
            $w['id[!=]'] = $blackAppIds;
        }

        $cateId = ints($d['cate_id']);
        $sort_by_nav = 0;
        if ($cateId) {
            switch ($cateId) {
                case  RX('common', 'invalid_cate_id')://下架
                    $w['status'] = 2;
                    $w['down_is_show'] = 1;
                    break;
                case  RX('common', 'mine_cate_id'):
                    //我做过的项目
                    $mine_appids = M()->select('x_tg_bb_data', 'distinct(appid) as appid', ['uid' => $uid, 'is_deleted' => 0]);
                    $mine_appids = array_column($mine_appids, 'appid');
                    $w['id'] = !empty($mine_appids) && isset($w['id']) ? array_intersect($mine_appids, (array)$w['id']) : $mine_appids;
                    unset($w['status']);
                    $w['OR'] = ['down_is_show' => 1, 'status' => 1];
                    break;
                case  1: //全部
                    $w['status'] = 1;
                    break;
                default:
                    $w['status'] = 1;
                    //热门的状态，新版允许不展示在列表
                    if (!M()->has('x_nav', ['status' => 1, 'id' => $cateId])) {//热门允许查询
                        return rs("该分类不存在");
                    }

                    $sort_by_nav = 1; //标记导航排序
                    $nav_app = M()->select('x_nav_app', 'distinct(appid) as appid,sort', ['nav_id' => $cateId, 'lx' => 1, 'is_del' => 0], 'sort desc');
                    $nav_app = array_column($nav_app, 'sort', 'appid');
                    $nav_appids = array_keys($nav_app);
                    $w['id'] = !empty($nav_appids) && isset($w['id']) ? array_intersect($nav_appids, (array)$w['id']) : $nav_appids;
                    if ($cateId == 2) {//如果有已推荐项目，并且开启推荐则强行加入到热门分类中
                        $open_tj_msg = (int)M()->get_field('x_tg_user_ext', 'open_tj_msg', ['id' => $uid]);
                        if ($open_tj_msg == 1) {
                            $tj_apps = M()->select('x_tj_app_msg', 'id,appid', ['uid' => $uid, 'created_at[>]' => date("Y-m-d H:i:s", time() - 7 * 86400)], 'id desc');
                            $tj_app_sort = array_column($tj_apps, 'id', 'appid');
                            $tj_appids = array_keys($tj_app_sort);
                            if (!empty($tj_appids)) {
                                $w["id"] = array_merge($w["id"], $tj_appids);
                            }
                        }
                    }
                    break;
            }
        }

        //todo IOS审核特殊处理
        $rwIds = null;
        if ($d['is_audit'] == 0 && $d['is_ios_audit'] == 1) {
            $ids = [593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612];
            $w['id'] = $ids;
            $w['status'] = 2;
            unset($w['OR']);
            $rwIds = array_column(M()->select('x_tg_app_rw', 'id', ['appid' => $ids]), 'id');
        }

        //用户佣金
        $yj_data = get_user_app_yj($uid, $rwIds, [], '', 4);
        if ($yj_data['code'] != 1) {
            return rs($yj_data['msg']);
        }

        $userAppYj = $yj_data['data'];
        $price_type = ints($d["price_type"]);
        $userAppMaxYJ = [];
        $price_appid = [];

        foreach ($userAppYj as $v) {
            if ($v['js_fs'] == 2) {
                $v['self_price'] = $v['self_price'] . '%';
            }
            switch ($price_type) {
                case 1: //5元以下
                    if ($v['self_price'] < 5) {
                        $userAppMaxYJ[$v['appid']][] = $v['self_price'];
                        $price_appid[] = $v['appid'];
                    }
                    break;
                case 2: //5-20元
                    if ($v['self_price'] >= 5 && $v['self_price'] <= 20) {
                        $userAppMaxYJ[$v['appid']][] = $v['self_price'];
                        $price_appid[] = $v['appid'];
                    }
                    break;
                case 3: //20-50元
                    if ($v['self_price'] > 20 && $v['self_price'] <= 50) {
                        $userAppMaxYJ[$v['appid']][] = $v['self_price'];
                        $price_appid[] = $v['appid'];
                    }
                    break;
                case 4://大于50元
                    if ($v['self_price'] > 50) {
                        $userAppMaxYJ[$v['appid']][] = $v['self_price'];
                        $price_appid[] = $v['appid'];
                    }
                    break;
                default:
                    $userAppMaxYJ[$v['appid']][] = $v['self_price'];
                    break;
            }
        }

        //同时筛选价格区间以及我做过的单
        if ($price_type) {
            $w['id'] = !empty($price_appid) && isset($w['id']) ? array_intersect($w['id'], array_unique($price_appid)) : $price_appid;
        }

        $tgUserZdlxs = M()->get_field('x_tg_user_info', 'bc_zdlx', ['id' => $uid]);
        $tgUserZdlxs = j2a($tgUserZdlxs);
        if (!empty($tgUserZdlxs)) {
            $where = M()->_build_where($w);
            $tgUserZdlxArr = [];
            foreach ($tgUserZdlxs as $tgUserZdlx) {
                $tgUserZdlxArr[] = ' bc_zdlxs like \'%"' . $tgUserZdlx . '"%\' ';
            }
            $tgUserZdlxArr = implode(" or ", $tgUserZdlxArr);

            //我做过的单 如果已筛选则不需要查询此sql
            if ($cateId != RX('common', 'mine_cate_id')) {
                $mine_appids = M()->select("x_tg_bb_data", "appid", ['status' => 1, 'is_deleted' => 0, 'uid' => $uid]);
                $mine_appids = array_column($mine_appids, "appid");
            }

            if (!empty($mine_appids)) {
                $mine_appids = "(" . implode(",", array_x($mine_appids)) . ")";
                $zdlxWhere = " and ($tgUserZdlxArr or id in $mine_appids)";
            } else {
                $zdlxWhere = " and ($tgUserZdlxArr)";
            }
            $where .= $zdlxWhere;
        } else {
            $where = M()->_build_where($w);
        }

        //价格排序
        if (in_array($d['self_price_order'], ['asc', 'desc'])) {
            if (!empty($d['is_smart_tj'])) {
                $appRes['data'] = M()->select('x_tg_app', 'id,name,status,image,has_qr,cate_id,is_recommend,recommend_star,dt_types,bc_zdlxs,zd_limit', $where, 'status asc,position ASC,is_recommend DESC,id desc', $d['limit']);
            } else {
                $appRes['data'] = M()->select('x_tg_app', 'id,name,status,image,has_qr,cate_id,is_recommend,recommend_star,dt_types,bc_zdlxs,zd_limit', $where, 'status asc,position ASC,is_recommend DESC,id desc');
            }

            $dataById = array_column($appRes['data'], null, "id");

            $userAppMaxYJSort = [];
            //针对任务价格进行排序
            foreach ($userAppMaxYJ as $appid => $jg) {
                $userAppMaxYJSort[$appid] = max($jg);
            }
            $d['self_price_order'] == 'asc' ? asort($userAppMaxYJSort) : arsort($userAppMaxYJSort);
            $data = [];
            foreach ($userAppMaxYJSort as $appid => $v) {
                if (isset($dataById[$appid])) {
                    $data[] = $dataById[$appid];
                }
            }
            $sort_by_nav = 0;
            $appRes["data"] = $data;
            $appRes['count'] = count($data);
        } else if ($sort_by_nav) {
            //根据导航排序
            $appRes['data'] = M()->select('x_tg_app', 'id,name,status,image,has_qr,cate_id,is_recommend,recommend_star,dt_types,zd_limit', $where, 'status asc,position ASC,is_recommend DESC,id desc');
            $appRes['count'] = count($appRes['data']);
        } else {
            $page = ints($d['page'], 1);
            $limit = ints($d['limit'], 5);
            $appRes = M()->s_page('x_tg_app', 'id,name,status,image,has_qr,cate_id,is_recommend,recommend_star,dt_types,bc_zdlxs,zd_limit', $where, 'status asc,position ASC,is_recommend DESC,id desc', $limit, $page);
        }

        $bbCountsMap = [];
        $data = [];
        if (!empty($appRes['data'])) {
            $appidArr = array_column($appRes['data'], 'id');
            //单个项目展示做单人数
            $bbCountsMap = m_tgapp::get_app_all_bb_counts();
            //总做单金额
            $all_money = m_tgapp::get_app_all_money() ?? [];
            $tgAppExt = array_column(M()->select('x_tg_app_ext', 'id,display_commission,display_bb_num', ['id' => $appidArr]), null, 'id');
            //结算数据截止日期
            $tg_notice = M()->select('x_tg_notice', 'app_id,MAX(end_date) as end_date', M()->_build_where(['app_id' => $appidArr, 'type' => x_config::NOTICE_TYPE_BILL]) . ' group by app_id');
            if ($tg_notice) {
                $tg_notice = array_column($tg_notice, null, 'app_id');
            }

            foreach ($appRes['data'] as $app) {
                $jl_money_type = 2;
                $maxMoney = isset($userAppMaxYJ[$app['id']]) ? reset($userAppMaxYJ[$app['id']]) : 0.00;
                if (strpos($maxMoney, '%') === false) {
                    $jl_money_type = 1;
                    $maxMoney = number_format($maxMoney, 2);
                }
                $appExt = $tgAppExt[$app['id']] ?? [];
                $bbInfo = isset($bbCountsMap[$app['id']]) ? j2a($bbCountsMap[$app['id']]) : [];
                $startTg = 1;
                $commission = float_add($appExt['display_commission'] ?? 0, $all_money[$app["id"]] ?? 0);
//                $commission = $all_money[$app["id"]] ?? 0;
                if (!isset($bbInfo['has_status_1']) || !$bbInfo['has_status_1']) {
                    $startTg = 0;
                    $commission = -1;
                }

                //todo IOS审核特殊处理
                if ($d['is_ios_audit'] == 1) {
                    $app['status'] = 1;
                }
                $bbNum = ($appExt['display_bb_num'] ?? 0) + ($bbInfo['num'] ?? 0);

                $xm_config = RC('xm_config');
                if (in_arrlb($app['id'], $xm_config['cpzs_appid'])) {
                    $all_money_text = $this->_chanpinzhishu($app['recommend_star']);
                } else {
                    $temp_money = $this->moneyFormat($commission, $maxMoney);
                    if ($commission <= 0) {
                        $all_money_text = '<span style="color:#51586F">数据计算中，敬请期待</span>';
                    } else {
                        $all_money_text = '<span style="color:#51586F">单次最高：</span> <span style="color:red">¥' . $temp_money . '</span>';
                    }
                }

                $temp = [
                    'app_id' => $app['id'],
                    'app_name' => $app['name'],
                    'app_logo' => url2oss($app['image']),
                    'zd_limit' => $app['zd_limit'],
                    'cate_id' => $d['cate_id'] ?? 0,
//                    'dt_type' => ints($app['dt_type']) > 0 ? ($app['dt_type'] == 1 ? 2 : 1) : 0,
                    'is_recommend' => $app['status'] == 2 ? 0 : ints($app['is_recommend']),
                    'app_status' => intval($app['status']), // 1 = 进行中，2 = 已下架
                    'app_status_txt' => $app['status'] == 1 ? '进行中' : '已失效', // 1 = 进行中，2 = 已下架
                    'is_start_tg' => $startTg,
                    'jl_money' => $app['status'] == 2 ? '已下架' : $maxMoney, //项目任务最大金额
                    'jl_money_type' => $app['status'] == 2 ? 0 : $jl_money_type,
                    'recommend_star' => floatval($app['recommend_star']),
                    'bb_num' => in_arrlb($app['id'], $xm_config['bbzero_appid']) ? 0 : $this->bbNumFormat($bbNum, $app['id']),
                    'all_money' => $temp_money ?? 0,
                    'all_money_text' => $all_money_text,
                    'dt_types' => jd($app['dt_types']),
                    'bill_end_date' => $tg_notice[$app['id']]['end_date'] ?? '',
                ];

                $temp['dt_type_list'] = $this->dt_type_list($temp['dt_types']);

                if (isset($nav_app[$app['id']])) {
                    $temp['sort'] = $nav_app[$app['id']];
                }

                if (!empty($tj_appids) && in_arrlb($app['id'], $tj_appids)) {
                    $temp['is_tj'] = 1;
                    $temp['sort'] = ($tj_app_sort[$app['id']] ?? 1) * 100000;
                }

                $data['app'][] = $temp;
            }
        }

        // 根据导航排序
        if ($sort_by_nav) {
            $data['app'] = arr_sort($data['app'], 'sort', 'desc');
        }

        if ($d['is_ios_audit'] == 1) {
            $nav_id = 1;
        }
        if ($d['is_audit'] == 1) {
            $nav_id = 15;
        }
        $cate = D('m_api')->get_index_cate($nav_id ?? []);
        if ($cate) {
            $cate = array_column($cate, null, 'id');
            if (issetx(2, $cate)) {
                unset($cate[2]);//剔除热门
            }
            $cate = array_values($cate);
        }
        $data['default_cate_id'] = ints($cate[0]['id']);
        $data['cate_data'] = $cate;
        $data['count'] = ints($appRes['count']);
        return rs('获取成功', 1, $data);
    }

    //标签转数组
    public function dt_type_list($dt_types)
    {
        $dt_types = glwb($dt_types);
        $zdlx_list = get_zdlx();
        $dt_type_list = [];
        if ($dt_types && is_array($dt_types)) {
            foreach ($dt_types as $v) {
                if (issetx($v, $zdlx_list)) {
                    $dt_type_list[] = [
                        'id' => $v,
                        'name' => $zdlx_list[$v] ?? '',
                    ];
                }
            }
        }
        return $dt_type_list;
    }

    private function _chanpinzhishu($recommend_star)
    {
        $zs = 5;
        $arr = array_filter(explode('.', $recommend_star));
        $str = '<span style="color:#51586F">产品指数：';

        if (count($arr) > 1) {
            for ($i = 1; $i <= $arr[0]; $i++) {
                $str .= '<img style="width:12px;" src="https://rtboss.bd.cn/up/site/0/20240531201337MIkzkGNGC5.png"></img>';
            }
            $str .= '<img style="width:12px;" src="https://rtboss.bd.cn/up/site/0/20240531201347wyotCsbMvX.png"></img>';
            for ($i = 1; $i <= $zs - $arr[0] - 1; $i++) {
                $str .= '<img style="width:12px;" src="https://rtboss.bd.cn/up/site/0/202405312013554vShUAQFXS.png"></img>';
            }
        } else {
            for ($i = 1; $i <= $arr[0]; $i++) {
                $str .= '<img style="width:12px;" src="https://rtboss.bd.cn/up/site/0/20240531201337MIkzkGNGC5.png"></img>';
            }
            for ($i = 1; $i <= $zs - $arr[0]; $i++) {
                $str .= '<img style="width:12px;" src="https://rtboss.bd.cn/up/site/0/202405312013554vShUAQFXS.png"></img>';
            }
        }
        $str .= '</span>';

        return $str;
    }

    /**
     * 获取用户推广的app信息
     * @param $d
     * @return array
     * @throws Exception
     */
    public function get_visitor_apps($d)
    {
        $d = glwb($d);
        $page = ints($d['page'], 1);
        $limit = ints($d['limit'], 5);
        $limit = min($limit, 20);

        $w = ['lx' => m_tgapp::TG_APP_LX_NORMAL, 'status' => 1];
        //游客模式隐藏项目
        $visitor_hide_appid = RC('visitor_hide_appid');
        $visitor_hide_appid = inta_x($visitor_hide_appid);
        if (!empty($visitor_hide_appid)) {
            $w['id[!=]'] = $visitor_hide_appid;
        }

        //如果筛选地推 网推 自动增加全部
        if ($d['dt_type']) {
            $w['dt_types[~]'] = '"' . $d['dt_type'] . '"';
        }

        if ($d['keywords']) {
            $w['name[~]'] = $d['keywords'];
            unset($w['status']);
            $w['OR'] = ['down_is_show' => 1, 'status' => 1];
        }

        if (issetx('xcx_bs', $d)) {
            if (in_arr($d['xcx_bs'], array_keys(x_config::XCX_BS_LABEL))) {
                $w['name[~]'] = x_config::XCX_BS_LABEL[$d['xcx_bs']]['keywords'] ?? '';
            }
        }

        $cateId = ints($d['cate_id']);
        $sort_by_nav = 0;
        if ($cateId) {
            switch ($cateId) {
                case  RX('common', 'invalid_cate_id')://下架
                    $w['status'] = 2;
                    $w['down_is_show'] = 1;
                    break;
                case  1: //全部
                    $w['status'] = 1;
                    break;
                default:
                    if (!M()->has("x_nav", ["status" => 1, "id" => $cateId])) {
                        return rs("该分类不存在");
                    }
                    $sort_by_nav = 1; //标记导航排序
                    $nav_app = M()->select("x_nav_app", "distinct(appid) as appid,sort", ["nav_id" => $cateId, 'lx' => 1, 'is_del' => 0], "sort desc");
                    $nav_app = array_column($nav_app, 'sort', 'appid');
                    $nav_appids = array_keys($nav_app);
                    $w['id'] = !empty($nav_appids) && isset($w['id']) ? array_intersect($nav_appids, (array)$w['id']) : $nav_appids;
                    break;
            }
        }

        //todo IOS审核特殊处理
        $rwIds = null;
        if ($d['is_audit'] == 0 && $d['is_ios_audit'] == 1) {
            $ids = [593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612];
            $w['id'] = $ids;
            $w['status'] = 2;
            unset($w['OR']);
            $rwIds = array_column(M()->select('x_tg_app_rw', 'id', ['appid' => $ids]), 'id');
        }

        $userAppYj = get_app_rws(0, $rwIds);
        $userAppYj = arr_sort_multi($userAppYj, ['sort' => 'desc', 'pt_price' => 'desc']);
        $price_type = ints($d["price_type"]);
        $userAppMaxYJ = [];
        $price_appid = [];
        foreach ($userAppYj as $v) {
            if ($v['js_fs'] == 2) {
                $v['pt_price'] = $v['pt_price'] . '%';
            }
            switch ($price_type) {
                case 1: //5元以下
                    if ($v['pt_price'] < 5) {
                        $userAppMaxYJ[$v['appid']][] = $v["pt_price"];
                        $price_appid[] = $v['appid'];
                    }
                    break;
                case 2: //5-20元
                    if ($v['pt_price'] >= 5 && $v['pt_price'] <= 20) {
                        $userAppMaxYJ[$v['appid']][] = $v["pt_price"];
                        $price_appid[] = $v['appid'];
                    }
                    break;
                case 3: //20-50元
                    if ($v['pt_price'] > 20 && $v['pt_price'] <= 50) {
                        $userAppMaxYJ[$v['appid']][] = $v["pt_price"];
                        $price_appid[] = $v['appid'];
                    }
                    break;
                case 4://大于50元
                    if ($v['pt_price'] > 50) {
                        $userAppMaxYJ[$v['appid']][] = $v["pt_price"];
                        $price_appid[] = $v['appid'];
                    }
                    break;
                default:
                    $userAppMaxYJ[$v['appid']][] = $v['pt_price'];
                    break;
            }
        }

        if ($price_appid) {
            $w['id'] = $price_appid;
        }
        $where = M()->_build_where($w);
        //价格排序
        if (in_array($d["self_price_order"], ["asc", "desc"])) {
            $appRes["data"] = M()->select("x_tg_app", 'id,name,status,image,has_qr,cate_id,is_recommend,recommend_star,dt_types,bc_zdlxs,zd_limit', $where);

            $dataById = array_column($appRes["data"], null, "id");

            $userAppMaxYJSort = [];
            //针对任务价格进行排序
            foreach ($userAppMaxYJ as $appid => $jg) {
                $userAppMaxYJSort[$appid] = max($jg);
            }
            $d["self_price_order"] == "asc" ? asort($userAppMaxYJSort) : arsort($userAppMaxYJSort);

            $data = [];
            foreach ($userAppMaxYJSort as $appid => $v) {
                if (isset($dataById[$appid])) {
                    $data[] = $dataById[$appid];
                }
            }
            $sort_by_nav = 0;
            $appRes["data"] = $data;
            $appRes["count"] = count($data);
        } else if ($sort_by_nav) {
            //根据导航排序
            $appRes['data'] = M()->select("x_tg_app", 'id,name,status,image,has_qr,cate_id,is_recommend,recommend_star,dt_types,zd_limit', $where);
            $appRes['count'] = count($appRes["data"]);
        } else {
            $appRes = M()->s_page('x_tg_app', 'id,name,status,image,has_qr,cate_id,is_recommend,recommend_star,dt_types,bc_zdlxs,zd_limit', $where, 'status asc,position ASC,is_recommend DESC', $limit, $page);
        }

        $bbCountsMap = [];
        $all_money = [];
        if (!empty($appRes['data'])) {
            $appidArr = array_column($appRes['data'], 'id');
            //单个项目展示做单人数
            $bbCountsMap = m_tgapp::get_app_all_bb_counts();
            //总做单金额
            $all_money = m_tgapp::get_app_all_money() ?? [];
            $tgAppExt = array_column(M()->select('x_tg_app_ext', 'id,display_commission,display_bb_num', ['id' => $appidArr]), null, 'id');
            //结算数据截止日期
            $tg_notice = M()->select('x_tg_notice', 'app_id,MAX(end_date) as end_date', M()->_build_where(['app_id' => $appidArr, 'type' => x_config::NOTICE_TYPE_BILL]) . ' group by app_id');
            if ($tg_notice) {
                $tg_notice = array_column($tg_notice, null, 'app_id');
            }
        }

        $data = [];
        foreach ($appRes['data'] as $app) {
            $jl_money_type = 2;
            $maxMoney = isset($userAppMaxYJ[$app['id']]) ? reset($userAppMaxYJ[$app['id']]) : 0.00;
            if (strpos($maxMoney, '%') === false) {
                $jl_money_type = 1;
                $maxMoney = number_format($maxMoney, 2);
            }

            $appExt = $tgAppExt[$app['id']] ?? [];
            $bbInfo = isset($bbCountsMap[$app['id']]) ? j2a($bbCountsMap[$app['id']]) : [];
            $startTg = 1;
            $commission = float_add($appExt['display_commission'] ?? 0, $all_money[$app["id"]] ?? 0);
//            $commission = $all_money[$app["id"]] ?? 0;
            if (!isset($bbInfo['has_status_1']) || !$bbInfo['has_status_1']) {
                $startTg = 0;
                $commission = -1;
            }
            $bbNum = ($appExt['display_bb_num'] ?? 0) + ($bbInfo['num'] ?? 0);
            $xm_config = RC('xm_config');
            if (in_arrlb($app['id'], $xm_config['cpzs_appid'])) {
                $all_money_text = $this->_chanpinzhishu($app['recommend_star']);
            } else {
                $temp_money = $this->moneyFormat($commission, $maxMoney);
                if ($commission <= 0) {
                    $all_money_text = '<span style="color:#828897">数据计算中，敬请期待</span>';
                } else {
                    $all_money_text = '<span style="color:#9ca4b9">单次最高：</span> <span style="color:red">¥' . $temp_money . '</span>';
                }
            }

            //todo IOS审核特殊处理
            if ($d['is_ios_audit'] == 1) {
                $app['status'] = 1;
            }

            $temp = [
                'app_id' => $app['id'],
                'app_name' => $app['name'],
                'app_logo' => url2oss($app['image']),
                'zd_limit' => $app['zd_limit'],
                'cate_id' => $d['cate_id'] ?? 0,
//                'dt_type' => ints($app['dt_type']) > 0 ? ($app['dt_type'] == 1 ? 2 : 1) : 0,
                'is_recommend' => $app['status'] == 2 ? 0 : ints($app['is_recommend']),
                "recommend_star" => floatval($app['recommend_star']),
                'app_status' => intval($app['status']), // 1 = 进行中，2 = 已下架
                'app_status_txt' => $app['status'] == 1 ? '进行中' : '已失效', // 1 = 进行中，2 = 已下架
                'is_start_tg' => $startTg,
                'jl_money' => $app['status'] == 2 ? '已下架' : $maxMoney, //项目任务最大金额
                'jl_money_type' => $app['status'] == 2 ? 0 : $jl_money_type,
                'bb_num' => in_arrlb($app['id'], $xm_config['bbzero_appid']) ? 0 : $this->bbNumFormat($bbNum, $app['id']),
                'all_money' => $temp_money ?? 0,
                'all_money_text' => $all_money_text,
                'dt_types' => jd($app['dt_types']),
                'bill_end_date' => $tg_notice[$app['id']]['end_date'] ?? '',
            ];

            $temp['dt_type_list'] = $this->dt_type_list($temp['dt_types']);

            if (isset($nav_app[$app['id']])) {
                $temp['sort'] = $nav_app[$app['id']];
            }

            $temp['label'] = '热门项目，价格稳定 ';
            if ($d['keywords'] == '小说') {
                $temp['label'] = '热门小说项目，价格稳定';
            } else if ($d['keywords'] == '短剧') {
                $temp['label'] = '热门短剧项目，价格稳定';
            } else if ($d['keywords'] == '网盘') {
                $temp['label'] = '大厂网盘项目，价格稳定';
            }

            $data['app'][] = $temp;
        }

        // 根据导航排序
        if ($sort_by_nav) {
            $data['app'] = arr_sort($data['app'], 'sort', 'desc');
        }

        if ($d['is_ios_audit'] == 1) {
            $nav_id = 1;
        }
        if ($d['is_audit'] == 1) {
            $nav_id = 15;
        }
        $cate = D('m_api')->get_index_cate($nav_id ?? []);
        if ($cate) {
            $cate = array_column($cate, null, 'id');
            if (issetx(2, $cate)) {
                unset($cate[2]);//剔除热门
            }
            $cate = array_values($cate);
        }
        $data['default_cate_id'] = ints($cate[0]["id"]);
        $data['cate_data'] = $cate;
        $data['count'] = ints($appRes['count']);
        return rs('获取成功', 1, $data);
    }

    /**
     * 金额格式化
     * @param $money
     * @param $price
     * @return string
     */
    private function moneyFormat($money, $price)
    {
        if ($money < 0) {
            return '0.00';
        }
        if ($money == 0) {
            $money = float_mul(floatval($price), 200);
        } else if ($money < 200) {
            $money = float_add($money, 900);
        } else if ($money < 800) {
            $money = float_add($money, 500);
        } else if ($money < 1000) {
            $money = float_add(float_mul(floor($money / 100), 200), $money);
        } else if ($money < 6000) {
            $money = float_add(float_mul(floor($money / 1000), 2000), $money);
        } else if ($money < 10000) {
            $money = float_add(float_mul(floor($money / 1000), 1700), $money);
        }

        $client = get_client_info();
        if ($client['app_type'] == 'android' && ver2num($client['app_version']) <= ver2num('3.2.0')) {
            return number_format($money, 2);
        }

        if ($money > 10000 * 10000) {//输出亿级
//            $money= bc('/',$money,10000*10000,0)."亿+";
            $money = '亿级';
        } elseif ($money > 1000 * 10000) {//输出千万级
//            $money= bc('/',$money,1000*10000,0)."千万+";
            $money = "千万级";
        } elseif ($money > 100 * 10000) {//输出xx.xx百万级
//            $money= bc('/',$money,100*10000,0)."百万+";
            $money = "百万级";
        } elseif ($money > 100000) {//输出xx.xx万
            $money = bc('/', $money, 10000, 0) . "万+";
        } else {
            $money = number_format($money, 2);
        }
        return $money;
    }

    /**
     * 报备数量格式化
     * @param $num
     * @param $appid
     * @return integer
     */
    private function bbNumFormat($num, $appid)
    {
        $num = ints($num);
        if ($num <= 0) {
            $num = $appid < 500 ? $appid : $appid * 3;
        } else if ($num < 500) {
            $num = $num * 3;
        } else if ($num < 1000) {
            $num = $num * 5;
        }

        return $num;
    }

    /**
     * 获取推广app详情
     *
     * @param array $d
     * @return array
     * @throws Exception
     */
    public function get_visitor_app_detail($d)
    {
        $d = glwb($d);
        $appid = ints($d['appid']);
        if (!$appid) {
            return rs('抱歉，缺少项目信息');
        }
        $where = ['id' => $appid];
        $app = M()->get('x_tg_app', 'id,name,status,image,comment,rw_jc,jc_video,rw_notice,icon_tag,tags
        ,zd_limit,jsbz_text,tgm_text,cxsj_text,pz_button_name,funcs,extra_data,open_zuodanjq,open_tj_pz', $where);

        if (!$app) {
            return rs('抱歉，当前项目已下架');
        }

        $app_rws = get_app_rws($appid);
        $app_rws = arr_sort($app_rws, 'sort', 'desc');
        M()->update("x_tg_app", 'click_num=click_num+1', ["id" => $appid]);
        foreach ($app_rws as &$v) {
            $v['self_price'] = $v['pt_price'];
            //已下架且状态不是审核通过 价格展示*号
            if ($app['status'] == 2) {
                $v['self_price'] = '*';
                continue;
            }

            if ($v['js_fs'] == 2) {
                $v['self_price'] = $v['self_price'] . '%';
                continue;
            }

            $v['self_price'] = '￥' . $v['self_price'];
        }

        $app_yj_rws = ['appid' => $app['id'], 'app_name' => $app['name'], 'app_logo' => url2oss($app['image']), 'rw_list' => array_values($app_rws)];

        // 标签
        $tags_arr = array_values(array_x(explode(',', $app['tags'] ?? '')));
        $icon_id = ints($app['icon_tag'] ?? '');
        $tag_ids = $tags_arr;
        $tag_ids[] = $icon_id;
        $tag_ids = array_values(array_x($tag_ids));
        $list_tags = [];
        if ($tag_ids) {
            $list_tags = M()->select('x_tg_app_tag', 'id,name,icon', ['id' => $tag_ids], '', count($tag_ids));
            foreach ($list_tags as &$val) {
                $val['icon'] = url2oss($val['icon']);
            }
            $list_tags = arr_key($list_tags, 'id');
        }

        $tags = [];
        if ($tags_arr) {
            foreach ($tags_arr as $tagid) {
                if (isset($list_tags[$tagid])) {
                    $tags[] = $list_tags[$tagid];
                }
            }
        }
        $icon_tag = empty($list_tags[$icon_id]['icon']) ? [] : $list_tags[$icon_id];
        $app_select_arr = RX('app.select');

        $tj_info_fileds = 0;
        if ($app['open_tj_pz'] == 1) {
            $tj_info_fileds = M()->has('x_tg_tj_info_filed', ['appid' => $appid, 'is_del' => 0, 'ht_lx' => 1]);
        }
        // 获取提交信息类型
        $app_notice = str_replace('<audio controls="controls" style="display: none;"></audio>', '', html_entity_decode($app['rw_notice']));
        $app_notice = str_replace('href="../', 'href="' . getym(true) . '/', $app_notice);

        $jc_video = j2a($app['jc_video']);
        foreach ($jc_video as &$value) {
            $value['video'] = url2oss($value['video']);
        }
        $more_button = [];
        $mappAppid = RC('mapp_appid');
        $open_mapp_tg = 0;
        $tg_button_name = '小程序挂载推广';
        if (in_arrlb($appid, $mappAppid)) {
            $open_mapp_tg = 1;
            $tg_button_name = '小程序推广';
            $more_button[] = ['bs' => 'mapp_appid', 'name' => '小程序推广', 'path' => ''];
        }

        $check_bb = $this->check_bb($app);
        $check_pz = $this->check_pz($app);

        $data = [
            'app_id' => $app['id'],
            'app_name' => $app['name'],
            'app_status' => intval($app['status']), // 1 = 进行中，2 = 已失效
            'app_logo' => url2oss($app['image']),
            'app_yq' => str_replace('<>', '＜＞', html_entity_decode($app['comment'])),
            'rw_jc' => str_replace('<>', '＜＞', html_entity_decode($app['rw_jc'])),
            'select_bb_item' => ['id' => 0, 'bs' => 'select_item', 'name' => $app_select_arr[$app['id']] ?? '推广渠道', 'type' => 3, 'is_required' => 1],
            'app_yj_rws' => $app_yj_rws,
            'tj_info_fileds' => $tj_info_fileds ? [1] : [],
            'jc_video' => $jc_video,
            'rw_notice' => $app_notice,
            'tags' => $tags,
            'icon_tag' => $icon_tag,
            'zd_limit' => ints($app["zd_limit"]),
            'agree_bb_cxcnh' => 0,
            'bb_cxcnh' => '',
            'cxsj_text' => str_replace('<>', '＜＞', html_entity_decode($app['cxsj_text'])),
            'tgm_text' => str_replace('<>', '＜＞', html_entity_decode($app['tgm_text'])),
            'jsbz_text' => str_replace('<>', '＜＞', html_entity_decode($app['jsbz_text'])),
            'relevance_app' => [],
            'pz_button_name' => $app['pz_button_name'],
            'open_mapp_tg' => $open_mapp_tg,
            'mapp_tg_button_name' => $tg_button_name,
            'forbid_bb_text' => $check_bb === true ? '' : $check_bb['msg'],
            'forbid_pz_text' => $check_pz === true ? '' : $check_pz['msg'],
            'open_zuodanjq' => ints($app['open_zuodanjq']),
        ];

        if (in_arrlb($app['id'], RXX('common', 'OPEN_BUY_KSGZ_APPID'))) {
            $data['ks_button_name'] = '快手挂载';
            $more_button[] = ['bs' => 'ks_guazai', 'name' => '快手挂载', 'path' => '/pages/ks/sq/index?appid=' . $appid];
        }

        $data['button_ext'] = [];
        $xcx_gz_appid = M()->get_field('x_site', 'value', ['bs' => 'xcx_gz_appid']);
        $xcx_gz_appid = j2a($xcx_gz_appid);
        if (in_arr($app['id'], $xcx_gz_appid)) {
            $more_button[] = ['bs' => 'xcx_gz_appid', 'name' => '小程序挂载', 'path' => '/pages/index/components/applet?appId=' . $appid . '&app_name=' . urlencode(base64_encode($app['name']))];
            $data['button_ext'] = [
                ['name' => '小程序挂载', 'path' => '/pages/index/components/applet?appId=' . $appid . '&app_name=' . urlencode(base64_encode($app['name']))],
//                ['name'=>'快手挂载','path'=>'/pages/ks/sq/index?appid='.$app['id']],
            ];
        }
        //视频代发
        $extra_data = j2a($app['extra_data']);
        $data['open_spdf'] = 0;
//        if ($extra_data['open_spdf'] == 1) {
//            $data['open_spdf'] = 1;
//            $more_button[] = ['bs' => 'spdf', 'name' => '视频代发', 'path' => '/pages/index/components/detail/newaddlist?appId=' . $appid . '&type=2&spdf=1'];
//        }
        $data['has_shudan'] = $extra_data['has_shudan'] ?? 0;
        if (!empty($app['funcs'])) {
            $funcs = M()->select('x_tg_app_func', 'id,name,bm,page_url', ['id' => inta_x(j2a($app['funcs'])), 'zt' => 1]);
            if (!empty($funcs)) {
                foreach ($funcs as $func) {
                    $more_button[] = [
                        'func_id' => $func['id'],
                        'name' => $func['bm'] ?: $func['name'],
                        'path' => $func['page_url'] . '?appid=' . $appid . '&func_id=' . $func['id'],
                    ];
                }
            }
        }
        $app_ext = M()->get('x_tg_app_ext', 'zuodanjq1,zuodanjq2,zuodanjq3,rw_jc_zysx,rw_jc_bc,rw_jc_qa,app_intro,app_group', ['id' => $appid]);
        if ($app['open_zuodanjq'] == 1) {
            $i = 0;
            if (!empty($app_ext['zuodanjq1'])) {
                $i++;
                $data['zuodanjq1'] = str_replace('href="../', 'href="' . getym(true) . '/', html_entity_decode($app_ext['zuodanjq1']));
            }
            if (!empty($app_ext['zuodanjq2'])) {
                $i++;
                $data['zuodanjq2'] = str_replace('href="../', 'href="' . getym(true) . '/', html_entity_decode($app_ext['zuodanjq2']));
            }
            if (!empty($app_ext['zuodanjq3'])) {
                $i++;
                $data['zuodanjq3'] = str_replace('href="../', 'href="' . getym(true) . '/', html_entity_decode($app_ext['zuodanjq3']));
            }

            if (!$i) {
                $data['open_zuodanjq'] = 2;
            }
        }

        $rw_jc_data = [];
        if (issetx('rw_jc_zysx', $app_ext)) {
            $rw_jc_data[] = [
                'key' => 'rw_jc_zysx',
                'name' => '注意事项',
                'nr' => $app_ext['rw_jc_zysx'] ? str_replace('<>', '＜＞', html_entity_decode($app_ext['rw_jc_zysx'])) : ''
            ];
        }
        if ($data['rw_jc']) {
            $rw_jc_data[] = [
                'key' => 'rw_jc',
                'name' => '推广流程',
                'nr' => $data['rw_jc']
            ];
        }
        if (issetx('rw_jc_bc', $app_ext)) {
            $rw_jc_data[] = [
                'key' => 'rw_jc_bc',
                'name' => '补充信息',
                'nr' => $app_ext['rw_jc_bc'] ? str_replace('<>', '＜＞', html_entity_decode($app_ext['rw_jc_bc'])) : ''
            ];
        }
        if (issetx('rw_jc_qa', $app_ext)) {
            $rw_jc_data[] = [
                'key' => 'rw_jc_qa',
                'name' => '常见问题',
                'nr' => $app_ext['rw_jc_qa'] ? str_replace('<>', '＜＞', html_entity_decode($app_ext['rw_jc_qa'])) : ''
            ];
        }
        $data['rw_jc_data'] = $rw_jc_data;

        $data['cxsj_text'] = str_replace('href="../', 'href="' . getym(true) . '/', $data['cxsj_text']);
        $data['tgm_text'] = str_replace('href="../', 'href="' . getym(true) . '/', $data['tgm_text']);
        $data['jsbz_text'] = str_replace('href="../', 'href="' . getym(true) . '/', $data['jsbz_text']);
        $data['rw_jc'] = str_replace('href="../', 'href="' . getym(true) . '/', $data['rw_jc']);

        $data['app_intro'] = $app_ext['app_intro'] ? str_replace('<>', '＜＞', html_entity_decode($app_ext['app_intro'])) : '';
        $data['app_group'] = $app_ext['app_group'] ? str_replace('<>', '＜＞', html_entity_decode($app_ext['app_group'])) : '';


        $data['cxsj_auth'] = 0;
        $new_sfsj_appid = array_column(array_values(m_sanfang_zz::APPS), 'app_id');
        if ($appid == 90 || in_arr($appid, $new_sfsj_appid)) {
            $data['cxsj_auth'] = 1;
        }

        $button = [];
        //todo 夸克网盘单独增加申请权益
        if ($appid == 90) {
            $button[] = ['bs' => '', 'name' => '申请权益', 'path' => '/pages/install/common/aboutdetail/detail?bs=quark_pan_quanyi_intro'];
        }

        if ($data['tj_info_fileds']) {
            //查询前置码是否限制凭证按钮显示
            $app_ext_hide_pz = M()->get('x_tg_app_ext', 'is_hide_pz', ['id' => $appid]);
            if ($app_ext_hide_pz['is_hide_pz'] != 1) {
                $button[] = ['bs' => 'apply_pz', 'name' => $app['pz_button_name'], 'path' => '/pages/index/components/detail/addlist?appId=' . $appid . '&type=1'];
            }
        }

        $button[] = ['bs' => 'apply_tg', 'name' => $d['is_ios_audit'] == 1 ? '联系沟通' : '申请推广', 'path' => '/pages/index/components/detail/newaddlist?appId=' . $appid . '&type=2'];
        if (!empty($more_button)) {
            if (count($more_button) >= 2) {
                $temp_button = [
                    'bs' => 'more_button', 'name' => '更多推广', 'path' => '', 'children' => $more_button
                ];
                array_unshift($button, $temp_button);
            } else {
                array_unshift($button, reset($more_button));
            }
        }

        $data['button'] = $button;
        return rs('获取成功', 1, $data);
    }

    /**
     * 获取推广app详情
     *
     * @param array $d
     * @param array $u
     * @return array
     * @throws Exception
     */
    public function get_app_detail($d, $u)
    {
        $d = glwb($d);
        $uid = ints($u['uid']);
        $appid = ints($d['appid']);
        // 更新
        $check = [
            ['intzs', $uid, '抱歉，用户登录已过期!'],
            ['intzs', $appid, '抱歉，请先选择项目!'],
        ];
        $ck = check($check);
        if ($ck !== true) {
            return rs($ck);
        }

        $app = M()->get('x_tg_app', 'id,bb_zd,uniq_bb_zd,name,status,image,comment,need_bb,bb_sm,rw_jc,has_qr,click_num,jc_video,rw_notice,icon_tag,tags,zd_limit,extra_data,open_bb_cxcnh,bb_cxcnh,open_bb_smrz,smrz_reason,jsbz_text,tgm_text,cxsj_text,pz_button_name,funcs,open_zuodanjq,open_tj_pz', ['id' => $appid]);
        if (empty($app)) {
            return rs('当前，当前推广项目不存在或已下架');
        }
        M()->update("x_tg_app", 'click_num=click_num+1', ["id" => $appid]);

//        $x_tg_bb_types = m_apprw_bb::get_appbb_types(['id' => $app['bb_zd']]);
//        //一个项目只能开启一个多条报备字段
//        $open_multi = 0;
//        foreach ($x_tg_bb_types as &$x_tg_bb_type) {
//            if ($open_multi == 1) {
//                $x_tg_bb_type['open_multi'] = 0;
//            }
//            $x_tg_bb_type["select_arr"] = explode(",", $x_tg_bb_type["data_select"]);
//            if ((count($x_tg_bb_type["select_arr"]) < 2) && $x_tg_bb_type["select_arr"][0] == "") {
//                $x_tg_bb_type["select_arr"] = null;
//            }
//            if ($x_tg_bb_type['open_multi']) {
//                $open_multi++;
//            }
//            $x_tg_bb_type['is_unique'] = (int)($x_tg_bb_type['id'] == $app['uniq_bb_zd']);
//        }
//        $x_tg_bb_types = m_apprw_bb::buildTree($x_tg_bb_types);
//        $select_bb_types = m_apprw_bb::format_bb_types($x_tg_bb_types);
//        $rw_ids = get_rw_id_by_appid($appid);
        $rw_ids = array_column(get_app_rws($appid, [], '', 4), 'id');
        $user_yj = [];
        if (!empty($rw_ids)) {
            $yj_data = get_user_app_yj($uid, $rw_ids, [], '', 4);
            if ($yj_data['code'] != 1) {
                return rs($yj_data['msg']);
            }

            $user_yj = $yj_data['data'];
            $is_set_qd_price = $yj_data['is_set_qd_price'];
        }
        $x_tg_bb_data = M()->get('x_tg_bb_data', 'status,bb_u_name,bb_u_sj', ['appid' => $appid, 'uid' => $uid, 'is_deleted' => 0], 'status asc');

        $max_price = 0;
        $max_rate = 0;
        array_walk($user_yj, function (&$v) use ($app, $x_tg_bb_data, &$max_price, &$max_rate) {
            //已下架且状态不是审核通过 价格展示*号
            if ($app['status'] == 2 && $x_tg_bb_data['status'] != 1) {
                return $v['self_price'] = '*';
            }

            if ($v['js_fs'] == 2) {
                if ($v['price'] > $max_rate) {
                    $max_rate = $v['price'];
                }
                $v['y_price'] = $v['price'] . '%';
                return $v['self_price'] = $v['self_price'] . '%';
            }
            if ($v['price'] > $max_price) {
                $max_price = $v['price'];
            }
            $v['y_price'] = '￥' . $v['price'];
            return $v['self_price'] = '￥' . $v['self_price'];
        });

        $app_yj_rws = [
            'appid' => $app['id'],
            'app_name' => $app['name'],
            'app_logo' => url2oss($app['image']),
            'rw_list' => $user_yj,
            'is_set_qd_price' => $is_set_qd_price,
            'max_price' => $max_price > 0 ? '￥' . $max_price : $max_rate . '%'
        ];

        // 获取提交信息类型
//        $tj_info_fileds = M()->select('x_tg_tj_info_filed', 'id,appid,name,type,data_type,data_select,help,tu_url,is_required,is_unique', ['appid' => $appid, 'is_del' => 0,'ht_lx'=>1]);
//
//        $tj_info_fileds = array_map(function ($item) {
//            $item['data_type'] = intval($item['data_type']);
//            if ($item['data_type'] == self::DATA_TYPE_SELECT) {
//                $item['select_arr'] = explode(',', $item['data_select']);
//            } else {
//                $item['select_arr'] = [];
//            }
//            // 转换是否必填为int类型
//            $item['is_required'] = intval($item['is_required']);
//            return $item;
//        }, $tj_info_fileds);

        $tj_info_fileds = 0;
        if ($app['open_tj_pz'] == 1) {
            $tj_info_fileds = M()->has('x_tg_tj_info_filed', ['appid' => $appid, 'is_del' => 0, 'ht_lx' => 1]);
        }
        // 标签
        $tags_arr = array_values(array_x(explode(',', $app['tags'] ?? '')));
        $icon_id = ints($app['icon_tag'] ?? '');
        $tag_ids = $tags_arr;
        $tag_ids[] = $icon_id;
        $tag_ids = array_values(array_x($tag_ids));
        $list_tags = [];
        if ($tag_ids) {
            $list_tags = M()->select('x_tg_app_tag', 'id,name,icon', ['id' => $tag_ids], '', count($tag_ids));
            foreach ($list_tags as &$val) {
                $val['icon'] = url2oss($val['icon']);
            }
            $list_tags = arr_key($list_tags, 'id');
        }

        $tags = [];
        if ($tags_arr) {
            foreach ($tags_arr as $tagid) {
                if (isset($list_tags[$tagid])) {
                    $tags[] = $list_tags[$tagid];
                }
            }
        }
        $icon_tag = empty($list_tags[$icon_id]['icon']) ? [] : $list_tags[$icon_id];
        $app_select_arr = RX('app.select');

        $yj_zd_num = 0;
        if ($app['zd_limit'] > 0) {
            $is_pj = M()->count('x_tg_tj_info_filed', ['appid' => $appid, 'is_del' => 0, 'ht_lx' => 1]); //是否凭证做单app
            if ($is_pj) { //凭证项目 已做单数量
                $yj_zd_num = M()->count('x_tg_tj_info', ['appid' => $appid, 'is_deleted' => 0]); //需求显示全部。数额不足由产品手动后台加数量
            } else {
                $yj_zd_num = M()->count('x_tg_detail', ['appid' => $appid, 'status' => 1]); //非凭证使用order表
            }
        }

        $app_notice = str_replace('<audio controls="controls" style="display: none;"></audio>', '', html_entity_decode($app['rw_notice']));
        $app_notice = str_replace('href="../', 'href="' . getym(true) . '/', $app_notice);

        $ext_field = 'pl_bb_excel_url,relevance_appid,rw_jc_zysx,rw_jc_bc,rw_jc_qa,app_intro,app_group';
        if ($app['open_zuodanjq'] == 1) {
            $ext_field .= ',zuodanjq1,zuodanjq2,zuodanjq3';
        }

        $app_ext = M()->get('x_tg_app_ext', $ext_field, ['id' => $app['id']]);
        $pl_bb_excel_url = $app_ext['pl_bb_excel_url'];
        $relevance_app_ids = array_x(j2a($app_ext['relevance_appid']));
        $relevance_app = [];
        if (count($relevance_app_ids) > 0 && $d['is_audit'] == 0) {
            $relevance_app = D('m_tgapp')->get_u_tg_apps(["app_ids" => $relevance_app_ids, "cate_id" => 1, "page" => 1, "limit" => 10, 'is_ios_audit' => $d['is_ios_audit']], $u);
            if ($relevance_app["code"] == 1) {
                $relevance_app = $relevance_app["data"]["app"];
            } else {
                $relevance_app = [];
            }
        }

        $jc_video = j2a($app['jc_video']);
        foreach ($jc_video as &$value) {
            $value['video'] = url2oss($value['video']);
        }

        $more_button = [];
        $mappAppid = RC('mapp_appid');
        $open_mapp_tg = 0;
        $tg_button_name = '小程序挂载推广';
        if (in_arrlb($appid, $mappAppid)) {
            $open_mapp_tg = 1;
            $tg_button_name = '小程序推广';
            $more_button[] = ['bs' => 'mapp_appid', 'name' => '小程序推广', 'path' => ''];
        }

        $check_bb = $this->check_bb($app);
        $check_pz = $this->check_pz($app);
        $college_tj = D('m_sxy')->get_college_nav($u, [
            'lx' => 2,
            'appid' => $app['id'],
            'limit' => 50
        ])['data'] ?? [];//项目推荐文章
        $data = [
            'app_id' => $app['id'],
            'app_name' => $app['name'],
            'app_status' => intval($app['status']), // 1 = 进行中，2 = 已失效
            'app_logo' => url2oss($app['image']),
            'app_yq' => str_replace('<>', '＜＞', html_entity_decode($app['comment'])),
            'rw_jc' => str_replace('<>', '＜＞', html_entity_decode($app['rw_jc'])),
            'bb_zd' => [],
            'select_bb_item' => ['id' => 0, 'bs' => 'select_item', 'name' => $app_select_arr[$app['id']] ?? '推广渠道', 'type' => 3, 'is_required' => 1],
            'select_bb_zd' => [],
            'bb_sm' => nl2br($app['bb_sm']),
            'app_yj_rws' => $app_yj_rws,
            'tj_info_fileds' => $tj_info_fileds ? [1] : [],
            'jc_video' => $jc_video,
            'rw_notice' => $u['channel_id'] == RX('common', 'custom_data_channel_id') ? '' : $app_notice,
            'tags' => $tags,
            'icon_tag' => $icon_tag,
            'zd_limit' => ints($app['zd_limit']),
            'yj_zd_num' => $yj_zd_num > ints($app['zd_limit']) ? ints($app['zd_limit']) : $yj_zd_num,
            'open_pl_bb' => !empty($pl_bb_excel_url) ? 1 : 0,
            'pl_bb_excel_url' => url2oss($pl_bb_excel_url ?: ''),
            'agree_bb_cxcnh' => 0,
            'bb_cxcnh' => '',
            'open_bb_smrz' => ints($app['open_bb_smrz']),
            'is_smrz' => 0,
            'smrz_reason' => '',
            'cxsj_text' => str_replace('<>', '＜＞', html_entity_decode($app['cxsj_text'])),
            'tgm_text' => str_replace('<>', '＜＞', html_entity_decode($app['tgm_text'])),
            'jsbz_text' => str_replace('<>', '＜＞', html_entity_decode($app['jsbz_text'])),
            'relevance_app' => $relevance_app,
            'pz_button_name' => $app['pz_button_name'],
            'open_mapp_tg' => $open_mapp_tg,
            'mapp_tg_button_name' => $tg_button_name,
            'forbid_bb_text' => $check_bb === true ? '' : $check_bb['msg'],
            'forbid_pz_text' => $check_pz === true ? '' : $check_pz['msg'],
            'open_zuodanjq' => ints($app['open_zuodanjq']),
            'app_intro' => str_replace('<>', '＜＞', html_entity_decode($app_ext['app_intro'] ?? '')),
            'app_group' => str_replace('<>', '＜＞', html_entity_decode($app_ext['app_group'] ?? '')),
            'college_tj' => $college_tj//课程推荐
        ];

        $rw_jc_data = [];
        if (issetx('rw_jc_zysx', $app_ext)) {
            $rw_jc_data[] = [
                'key' => 'rw_jc_zysx',
                'name' => '注意事项',
                'nr' => $app_ext['rw_jc_zysx'] ? str_replace('<>', '＜＞', html_entity_decode($app_ext['rw_jc_zysx'])) : ''
            ];
        }
        if ($data['rw_jc']) {
            $rw_jc_data[] = [
                'key' => 'rw_jc',
                'name' => '推广流程',
                'nr' => $data['rw_jc']
            ];
        }
        if (issetx('rw_jc_bc', $app_ext)) {
            $rw_jc_data[] = [
                'key' => 'rw_jc_bc',
                'name' => '补充信息',
                'nr' => $app_ext['rw_jc_bc'] ? str_replace('<>', '＜＞', html_entity_decode($app_ext['rw_jc_bc'])) : ''
            ];
        }
        if (issetx('rw_jc_qa', $app_ext)) {
            $rw_jc_data[] = [
                'key' => 'rw_jc_qa',
                'name' => '常见问题',
                'nr' => $app_ext['rw_jc_qa'] ? str_replace('<>', '＜＞', html_entity_decode($app_ext['rw_jc_qa'])) : ''
            ];
        }

        $data['rw_jc_data'] = $rw_jc_data;

        if (in_arrlb($app['id'], RXX('common', 'OPEN_BUY_KSGZ_APPID'))) {
            $data['ks_button_name'] = '快手挂载';
            $more_button[] = ['bs' => 'ks_guazai', 'name' => '快手挂载', 'path' => '/pages/ks/sq/index?appid=' . $appid];
        }

        $data['button_ext'] = [];
        $xcx_gz_appid = M()->get_field('x_site', 'value', ['bs' => 'xcx_gz_appid']);
        $xcx_gz_appid = j2a($xcx_gz_appid);
        if (in_arr($app['id'], $xcx_gz_appid)) {
            $more_button[] = ['bs' => 'xcx_gz_appid', 'name' => '小程序挂载', 'path' => '/pages/index/components/applet?appId=' . $appid . '&app_name=' . urlencode(base64_encode($app['name']))];
            $data['button_ext'] = [
                ['name' => '小程序挂载', 'path' => '/pages/index/components/applet?appid=' . $appid . '&app_name=' . urlencode(base64_encode($app['name']))],
//                ['name'=>'快手挂载','path'=>'/pages/ks/sq/index?appid='.$app['id']],
            ];
        }
        //视频代发
        $extra_data = j2a($app['extra_data']);
        $data['open_spdf'] = 0;
//        if ($extra_data['open_spdf'] == 1) {
//            $data['open_spdf'] = 1;
//            $more_button[] = ['bs' => 'spdf', 'name' => '视频代发', 'path' => '/pages/index/components/detail/newaddlist?appId=' . $appid . '&type=2&spdf=1'];
//        }
        $data['has_shudan'] = $extra_data['has_shudan'] ?? 0;
        if (!empty($app['funcs'])) {
            $funcs = M()->select('x_tg_app_func', 'id,name,bm,page_url', ['id' => inta_x(j2a($app['funcs'])), 'zt' => 1]);
            if (!empty($funcs)) {
                foreach ($funcs as $func) {
                    $more_button[] = [
                        'func_id' => $func['id'],
                        'bs' => '',
                        'name' => $func['bm'] ?: $func['name'],
                        'path' => $func['page_url'] . '?appid=' . $appid . '&func_id=' . $func['id'],
                    ];
                }
            }
        }
        if ($app['open_bb_cxcnh'] == 1 && !empty($app['bb_cxcnh'])) {
            $data['agree_bb_cxcnh'] = (int)M()->has('x_tg_app_cxcnh', ['appid' => $appid, 'uid' => $uid, 'content' => $app['bb_cxcnh']]);
            if (!$data['agree_bb_cxcnh']) {
                $data['bb_cxcnh'] = str_replace('<>', '＜＞', html_entity_decode($app['bb_cxcnh']));
                $data['bb_cxcnh'] = str_replace('href="../', 'href="' . getym(true) . '/', $data['bb_cxcnh']);
            }
        }
        if ($app['open_bb_smrz'] == 1 && !empty($app['smrz_reason'])) {
            //todo 企业子账号必须实名
            $smrz = m_user_main::user_has_smrz($u);
            $data['is_smrz'] = 1;
            if ($smrz['zt'] != m_smrz::ZT_OK) {//如果已实名认证，无需返回
                $data['is_smrz'] = 0;
                $data['smrz_reason'] = str_replace('<>', '＜＞', html_entity_decode($app['smrz_reason']));
                $data['smrz_reason'] = str_replace('href="../', 'href="' . getym(true) . '/', $data['smrz_reason']);
            }
        }
        if ($app['open_zuodanjq'] == 1) {
            $i = 0;
            if (!empty($app_ext['zuodanjq1'])) {
                $i++;
                $data['zuodanjq1'] = str_replace('href="../', 'href="' . getym(true) . '/', html_entity_decode($app_ext['zuodanjq1']));
            }
            if (!empty($app_ext['zuodanjq2'])) {
                $i++;
                $data['zuodanjq2'] = str_replace('href="../', 'href="' . getym(true) . '/', html_entity_decode($app_ext['zuodanjq2']));
            }
            if (!empty($app_ext['zuodanjq3'])) {
                $i++;
                $data['zuodanjq3'] = str_replace('href="../', 'href="' . getym(true) . '/', html_entity_decode($app_ext['zuodanjq3']));
            }

            if (!$i) {
                $data['open_zuodanjq'] = 2;
            }
        }
        $data['app_yq'] = str_replace('href="../', 'href="' . getym(true) . '/', $data['app_yq']);
        $data['cxsj_text'] = str_replace('href="../', 'href="' . getym(true) . '/', $data['cxsj_text']);
        $data['tgm_text'] = str_replace('href="../', 'href="' . getym(true) . '/', $data['tgm_text']);
        $data['jsbz_text'] = str_replace('href="../', 'href="' . getym(true) . '/', $data['jsbz_text']);
        $data['rw_jc'] = str_replace('href="../', 'href="' . getym(true) . '/', $data['rw_jc']);
        $app_tip_popup = D('m_apprw_tips')->get_user_app_popup($uid, $appid);
        if (!empty($app_tip_popup)) {
            $app_tip_popup['app_name'] = $data['app_name'];
            $app_tip_popup['app_logo'] = $data['app_logo'];
        }
        $data['app_tip_popup'] = $app_tip_popup;
        //获取项目最新项目类型公告、项目变更公告时间
        $gg_time_cache = D('m_admin_tgnotice')->gg_time_cache($app['id']);
        $data['gg_xmjs_time'] = $gg_time_cache['data']['gg_xmjs_time'] ?? '';
        $data['gg_xmbg_time'] = $gg_time_cache['data']['gg_xmbg_time'] ?? '';

        // 夸克网盘查询数据和新版三方数据查询地址特殊处理
        $data['cxsj_auth'] = 0;
        $data['copy_url'] = '';

        $new_sfsj_appid = array_column(array_values(m_sanfang_zz::APPS), 'app_id');
        if (in_arr($appid, $new_sfsj_appid)) {
            $data['cxsj_auth'] = 1;
            $data['copy_url'] = D('m_sanfang_zz')->get_sfsjcx_url($u, ['app_id' => $appid]);
        }

        $button = [];
        //todo 夸克网盘单独增加申请权益
        if ($appid == 90) {
            $button[] = ['bs' => '', 'name' => '申请权益', 'path' => '/pages/install/common/aboutdetail/detail?bs=quark_pan_quanyi_intro'];
        }

        if ($data['tj_info_fileds']) {
            //查询前置码是否限制凭证按钮显示
            $app_ext_hide_pz = M()->get('x_tg_app_ext', 'is_hide_pz', ['id' => $appid]);
            if ($app_ext_hide_pz['is_hide_pz'] != 1) {
                $button[] = ['bs' => 'apply_pz', 'name' => $app['pz_button_name'], 'path' => '/pages/index/components/detail/addlist?appId=' . $appid . '&type=1'];
            }
        }

        $button[] = ['bs' => 'apply_tg', 'name' => $d['is_ios_audit'] == 1 ? '联系沟通' : '申请推广', 'path' => '/pages/index/components/detail/newaddlist?appId=' . $appid . '&type=2'];

        if (!empty($more_button)) {
            if (count($more_button) >= 2) {
                $temp_button = [
                    'bs' => 'more_button', 'name' => '更多推广', 'path' => '', 'children' => $more_button
                ];
                array_unshift($button, $temp_button);
            } else {
                array_unshift($button, reset($more_button));
            }
        }

        $data['button'] = $button;
        return rs('获取成功', 1, $data);
    }

    /**
     * 获取优酷项目详情
     */
    public function get_yk_detail($uid, $d)
    {
        $u = glwb($u);
        $uid = ints($uid);
        $d = glwb($d);

        $rules = [
            ['intzs', $uid, '登录超时，请重新登录！'],
            ['intzs', $d['appid'], '请填写项目ID！'],
            ['same', [$d['appid'], 1006], '请填写项目ID！'],
        ];
        $ck = check($rules);
        if ($ck !== true) {
            return rs('抱歉，' . $ck);
        }
        $app = M()->get('x_tg_app', 'id,bb_zd,uniq_bb_zd,name,status,image,comment,need_bb,bb_sm,rw_jc,has_qr,click_num,jc_video,rw_notice,icon_tag,tags,zd_limit,extra_data,open_bb_cxcnh,bb_cxcnh,open_bb_smrz,smrz_reason,jsbz_text,tgm_text,cxsj_text,pz_button_name,funcs,open_zuodanjq,open_tj_pz', ['id' => $appid]);
        if (empty($app)) {
            return rs('当前，当前推广项目不存在或已下架');
        }
        M()->update("x_tg_app", 'click_num=click_num+1', ["id" => $appid]);
        
$rw_ids = array_column(get_app_rws($appid, [], '', 4), 'id');
        $user_yj = [];
        if (!empty($rw_ids)) {
            $yj_data = get_user_app_yj($uid, $rw_ids, [], '', 4);
            if ($yj_data['code'] != 1) {
                return rs($yj_data['msg']);
            }

            $user_yj = $yj_data['data'];
            $is_set_qd_price = $yj_data['is_set_qd_price'];
        }
        $x_tg_bb_data = M()->get('x_tg_bb_data', 'status,bb_u_name,bb_u_sj', ['appid' => $appid, 'uid' => $uid, 'is_deleted' => 0], 'status asc');

        $max_price = 0;
        $max_rate = 0;
        array_walk($user_yj, function (&$v) use ($app, $x_tg_bb_data, &$max_price, &$max_rate) {
            //已下架且状态不是审核通过 价格展示*号
            if ($app['status'] == 2 && $x_tg_bb_data['status'] != 1) {
                return $v['self_price'] = '*';
            }

            if ($v['js_fs'] == 2) {
                if ($v['price'] > $max_rate) {
                    $max_rate = $v['price'];
                }
                $v['y_price'] = $v['price'] . '%';
                return $v['self_price'] = $v['self_price'] . '%';
            }
            if ($v['price'] > $max_price) {
                $max_price = $v['price'];
            }
            $v['y_price'] = '￥' . $v['price'];
            return $v['self_price'] = '￥' . $v['self_price'];
        });

        $app_yj_rws = [
            'appid' => $app['id'],
            'app_name' => $app['name'],
            'app_logo' => url2oss($app['image']),
            'rw_list' => $user_yj,
            'is_set_qd_price' => $is_set_qd_price,
            'max_price' => $max_price > 0 ? '￥' . $max_price : $max_rate . '%'
        ];
    }

    //获取项目报备基础信息
    public function share_get_app_bm_info($d)
    {
        $d = glwb($d);
        $res = D('m_apprw_main')->check_share_token($d, 4);
        if ($res['code'] != 1) {
            return rs($res['msg']);
        }
        $uid = $res['data']['uid'];
        $appid = $res['data']['appid'];
        return $this->get_app_bm_info(['appid' => $appid], $uid);
    }

//    /**报备模板提交查询
//     * @param $u
//     * @param $d
//     * @return array|false|mixed|string
//     */
//    public function bb_excel_query($u, $d)
//    {
//        $u = glwb($u);
//        $d = glwb($d);
//        $uid = ints($u['uid']);
//
//        $rule = [
//            ['intzs', $uid, '登录超时，请重新登录！'],
//            ['intzs', $d['excel_id'], '请填写表格ID！'],
//        ];
//        $ck = check($rule);
//        if ($ck !== true) {
//            return rs('抱歉，' . $ck);
//        }
//
//        $row = M()->get('x_tg_user_excel', 'id,uid,appid,status,reason', ['id' => $d['excel_id'], 'uid' => $uid, 'type' => 1]);
//        if (!$row) {
//            return rs("抱歉，记录不存在");
//        }
//
//        return rs('ok', 1, [
//            'status' => $row['status'],
//            'reason' => $row['reason'],
//        ]);
//    }
//
//    /**报备模板提交
//     * @param $u
//     * @param $d
//     * @return array|false|mixed|string
//     */
//    public function bb_excel_put($u, $d)
//    {
//        $u = glwb($u);
//        $d = glwb($d);
//        $uid = ints($u['uid']);
//
//        $rule = [
//            ['intzs', $uid, '登录超时，请重新登录！'],
//            ['intzs', $d['appid'], '请选择项目！'],
//            ['mblen', [$d['excel_path'], 1, 255], '请先上传文件！'],
//        ];
//        $ck = check($rule);
//        if ($ck !== true) {
//            return rs('抱歉，' . $ck);
//        }
//
//        //加锁
//        $hcm = __METHOD__ . ':bb_excel_put:' . $uid;
//        $suo = lock($hcm, 10);
//        if ($suo == 0) {
//            return rs("抱歉，系统繁忙，请重试");
//        }
//
//        //先判断文件是否存在
//        if (!file_exists(LOG . $d['excel_path'])) {
//            unlock($hcm, $suo);
//            return rs("抱歉，请先上传文件");
//        }
//        //存表
//        $sj = getsj2();
//        $id = M()->insert('x_tg_user_excel', [
//            'uid' => $uid,
//            'appid' => $d['appid'],
//            'excel_path' => $d['excel_path'],
//            'type' => 1,
//            'status' => 2,
//            'create_time' => $sj,
//            'update_time' => $sj,
//        ], 1);
//        if (!$id) {
//            unlock($hcm, $suo);
//            return rs("抱歉，批量报备失败");
//        }
//        $d['excel_id'] = $id;
////        if (BENDI == 1) {
////            $data = [
////                'params' => je([
////                    'u' => $u,
////                    'd' => $d,
////                    'op' => 'bb'
////                ])
////            ];
////            $ret = D('m_queue_task_readexcel')->do($data);
////            unlock($hcm, $suo);
////            return $ret;
////        }
//
//        //拿到文件执行队列
//        $task_res = D('m_queue_task_readexcel')->add(0, 0, [
//            'u' => ['uid' => $u['uid']],
//            'd' => $d,
//            'op' => 'bb'
//        ], 1);
//        if ($task_res['code'] != 1) {
//            unlock($hcm, $suo);
//            return $task_res;
//        }
//        unlock($hcm, $suo);
//        return rs('文件已提交', 1, ['excel_id' => $id]);
//    }
//
//    /**报备模板
//     * @param $u
//     * @param $d
//     * @return array|false|mixed|string
//     * @throws Exception
//     */
//    public function bb_excel_tpl($u, $d)
//    {
//        $u = glwb($u);
//        $d = glwb($d);
//        $uid = ints($u['uid']);
//        if (!$uid) {
//            $res = D('m_apprw_main')->check_share_token($d);
//            if ($res['code'] != 1) {
//                return rs($res['msg']);
//            }
//            $uid = $res['data']['uid'];
//        }
//        $rule = [
//            ['intzs', $uid, '登录超时，请重新登录！'],
//            ['intzs', $d['appid'], '请选择项目！'],
//        ];
//        $ck = check($rule);
//        if ($ck !== true) {
//            return rs('抱歉，' . $ck);
//        }
//
//        $ret = $this->get_app_bm_info(['appid' => $d['appid']], $uid);
//        $bb_zd = $ret['data']['bb_zd'] ?? [];
//        if (!$bb_zd) {
//            return rs('抱歉，未找到报备模板!');
//        }
//        $header = [];
//        foreach ($bb_zd as $v) {
//            $header[] = $v['name'];
//        }
//        $data = [
//            $header
//        ];
//
//        $this->save_exl(($ret['data']['app_name'] ?? '') . '报备模板', $data);
//    }

    /**凭证进度条换成
     * @param $excel_id
     * @return string
     */
    public function pz_excel_progress_index($excel_id)
    {
        return 'pz_excel_progress:' . $excel_id;
    }

    /**凭证模板提交查询
     * @param $u
     * @param $d
     * @return array|false|mixed|string
     */
    public function pz_excel_query($u, $d)
    {
        $u = glwb($u);
        $d = glwb($d);
        $uid = ints($u['uid']);

        $rule = [
            ['intzs', $uid, '登录超时，请重新登录！'],
            ['intzs', $d['excel_id'], '请填写表格ID！'],
        ];
        $ck = check($rule);
        if ($ck !== true) {
            return rs('抱歉，' . $ck);
        }

        $max_cache_index = 'pz_excel_query:' . $d['excel_id'];
        $query_num = S($max_cache_index);
        $query_max = 150;
        if (in_arr(ENV_HJ, ['dev', 'test'])) {
            $query_max = 60;
        }
        if ($query_num > $query_max) {
            return rs("抱歉，查询超时");
        }

        $row = M()->get('x_tg_user_excel', 'id,uid,appid,status,reason', ['id' => $d['excel_id'], 'uid' => $uid, 'type' => 2]);
        if (!$row) {
            return rs("抱歉，记录不存在");
        }

        if ($row['status'] == 1) {
            $progress = 100;
        } else {
            $cahce_index = $this->pz_excel_progress_index($d['excel_id']);
            $cahce_data = S($cahce_index);
            $progress = 1;
            if ($cahce_data) {
                $c = time() - $cahce_data['start_time'] ?? 0;
                $row_num = $cahce_data['row_num'];
                $min = 1;
                if ($row_num > 500) {
                    //超过500条按三分钟估
                    $min = 3;
                }
                $progress = round($c / (60 * $min) * 100, 2);
                if ($progress >= 100) {
                    $progress = 99;
                }
            }
        }
        S($max_cache_index, $query_num + 1, 3600);
        return rs('ok', 1, [
            'status' => $row['status'],
            'reason' => $row['reason'],
            'progress' => ints($progress),
//            'cahce_data' => $cahce_data??[]
        ]);
    }

    /**
     * 报备模板提交
     * @param $u
     * @param $d
     * @return array|false|mixed|string
     * @throws Exception
     */
    public function pz_excel_put($u, $d)
    {
        $u = glwb($u);
        $d = glwb($d);
        $uid = ints($u['uid']);
        if (!$uid) {
            $res = D('m_apprw_main')->check_share_token($d, 5);
            if ($res['code'] != 1) {
                return rs($res['msg']);
            }
            $uid = $res['data']['uid'];
        }
        $rule = [
            ['intzs', $uid, '登录超时，请重新登录！'],
            ['intzs', $d['appid'], '请选择项目！'],
            ['mblen', [$d['excel_path'], 1, 255], '请先上传文件！'],
        ];
        $ck = check($rule);
        if ($ck !== true) {
            return rs('抱歉，' . $ck);
        }

        //加锁
        $hcm = __METHOD__ . ':pz_excel_put:' . $uid;
        $suo = lock($hcm, 10);
        if ($suo == 0) {
            if (BENDI == 0) {
                return rs("抱歉，系统繁忙，请重试");
            }
        }

        //先判断文件是否存在
        if (!file_exists(LOG . $d['excel_path'])) {
            unlock($hcm, $suo);
            return rs("抱歉，请先上传文件");
        }
        //存表
        $sj = getsj2();
        $id = M()->insert('x_tg_user_excel', [
            'uid' => $uid,
            'appid' => $d['appid'],
            'excel_path' => $d['excel_path'],
            'type' => 2,
            'status' => 2,
            'create_time' => $sj,
            'update_time' => $sj,
        ], 1);
        if (!$id) {
            unlock($hcm, $suo);
            return rs("抱歉，批量报备失败");
        }
        $d['excel_id'] = $id;
        if (BENDI == 1) {
            $data = [
                'params' => je([
                    'u' => ['uid' => $uid],
                    'd' => $d,
                    'op' => 'pz'
                ])
            ];
            $ret = D('m_queue_task_readexcel')->do($data);
            unlock($hcm, $suo);
            $ret['excel_id'] = $id;
            return $ret;
        }

        //拿到文件执行队列
        $task_res = D('m_queue_task_readexcel')->add(0, 0, [
            'u' => ['uid' => $uid],
            'd' => $d,
            'op' => 'pz'
        ], 1);
        if ($task_res['code'] != 1) {
            unlock($hcm, $suo);
            return $task_res;
        }
        unlock($hcm, $suo);
        return rs('文件已提交', 1, ['excel_id' => $id ?? '']);
    }

    /**凭证模板
     * @param $u
     * @param $d
     * @return array|false|mixed|string
     * @throws Exception
     */
    public function pz_excel_tpl($u, $d)
    {
        $u = glwb($u);
        $d = glwb($d);
        $uid = ints($u['uid'] ?? 0);
        $download = ints($d['download'] ?? 0);
        if (!$uid) {
            $res = D('m_apprw_main')->check_share_token($d, 5);
            if ($res['code'] != 1) {
                ajaxReturn(rs($res['msg']));
            }
            $uid = $res['data']['uid'];
            $cahce_index = $res['data']['cahce_index'] ?? '';
            if ($cahce_index) {
                $d = S($cahce_index);
                if (!$d) {
                    return rs('抱歉，链接已过期!');
                }
                $download = 1;
            }
        }

        $rule = [
            ['intzs', $uid, '登录超时，请重新登录！'],//下载表格可以不登录
            ['intzs', $d['appid'], '请选择项目！'],
            ['len', [$d['bb_id'], 0, 2000], '请选择推广码！'],
        ];
        $ck = check($rule);
        if ($ck !== true) {
            return rs('抱歉，' . $ck);
        }

        //开启凭证
        $app = M()->get('x_tg_app', 'id,open_tj_pz,uniq_bb_zd,extra_data,status', ['id' => $d['appid']]);
        if (empty($app) || $app['open_tj_pz'] != 1) {
            return rs('抱歉，当前项目未开启凭证提交!');
        }
        $extra_data = j2a($app['extra_data']);
        // 项目下架后是否允许提交凭证
        if ($app['status'] == 2 && (!issetx('down_allow_pz', $extra_data) || $extra_data['down_allow_pz'] != 1)) {
            return rs('抱歉，当前项目未开启凭证提交!');
        }

        $bb_types = get_bb_types(['id' => $app['uniq_bb_zd']]);
        $bb_type_name = $bb_types[0]['name'] ?? '';
        if (!$bb_type_name) {
            return rs('抱歉，当前项目未设置唯一字段!');
        }

        $bb_ids = explode(',', $d['bb_id']);
        $bb = M()->select('x_tg_bb_data', 'id,uniq_bb_value', ['id' => $bb_ids, 'uid' => $uid, 'appid' => $d['appid'], 'status' => 1, 'is_deleted' => 0], 'id desc', 5000);
        if (!$bb) {
            return rs('抱歉，请选择推广码！');
        }
        $bb_ids = array_column($bb, 'id');
        if (!$bb_ids) {
            return rs('抱歉，请选择推广码');
        }
        $uniq_bb_value_arr = array_column($bb, 'uniq_bb_value');

        $ret = $this->get_app_bm_info(['appid' => $d['appid']], $uid);
        $tj_info_fileds = $ret['data']['tj_info_fileds'] ?? [];
        if (!$tj_info_fileds) {
            return rs('抱歉，未找到报备模板!');
        }

        if ($download == 0) {
            $cahce_index = 'pz_excel_tpl:' . $uid . time() . rand(1000, 9999);
            S($cahce_index, $d, 60 * 30);
            $share_data = ['uid' => $uid, 'action' => 'download_pz_tpl', 'cahce_index' => $cahce_index];
            return rs('获取成功', 1, ['excel_url' => getym(true) . '/api_app/pz_excel_tpl?download=1&invite_pz_token=' . jiami($share_data)]);
        }

        $header = ["报备唯一标识({$bb_type_name})"];
        foreach ($tj_info_fileds as $v) {
            if ($v['type'] != 3) {
                //视频不参与
                $header[] = $v['name'];
            }
        }
        $data = [
            $header
        ];
        foreach ($uniq_bb_value_arr as $v) {
            $data[] = [$v];
        }

        $this->save_exl(($ret['data']['app_name'] ?? '') . '凭证模板', $data);
    }

    //清楚用户批量上传表格缓存文件
    public function clear_excel()
    {
        $list = M()->select('x_tg_user_excel', 'id,excel_path,status', ['create_time[<=]' => date('Y-m-d H:i:s', time() - 60 * 60), 'status' => [2, 3]], '', 500);
        $up = [];
        foreach ($list as $v) {
            if ($v['status'] == 2) {
                //处理中的标记失败
                $up[] = [
                    'id' => $v['id'],
                    'status' => 3,
                    'reason' => '处理超时'
                ];
            }
            $file_path = LOG . $v['excel_path']; // Excel文件路径
            @unlink($file_path);
        }
        if ($up) {

            $up_ret = M()->updates('x_tg_user_excel', $up, 'id');
            if ($up_ret === false) {
                return rs('状态保存失败');
            }
        }
        return rs('历史数据已处理');
    }

    /**
     * 获取项目报备基础信息
     * @param array $d
     * @param array $uid
     * @return array
     * @throws Exception
     */
    public function get_app_bm_info($d, $uid)
    {
        $d = glwb($d);
        $uid = ints($uid);

        $from_token = 0;
        if (!$uid) {
            $res = D('m_apprw_main')->check_share_token($d, 4);
            if ($res['code'] != 1) {
                return rs($res['msg']);
            }
            $from_token = 1;
            $uid = $res['data']['uid'];
        }

        $user = m_user_main::get_user($uid, 'id,sub_user_lx,smrz_lx,master_uid,status,ywy_uid,admin_tg_uid,p_uid');
        if (empty($user)) {
            return rs('抱歉，团长账号状态异常，链接失效!');
        }
        $user['uid'] = $user['id'];
        $check_user_zt = m_user_main::check_user_zt($user);
        if ($check_user_zt['code'] != 1) {
            return rs($check_user_zt['msg']);
        }

        $appid = ints($d['appid']);
        if (!$appid) {
            return rs('抱歉，缺少推广项目ID');
        }
        $where = ['id' => $appid];
        $app = M()->get('x_tg_app', 'id,bb_zd,uniq_bb_zd,name,bb_sm,rw_notice,status,open_bb_cxcnh,bb_cxcnh,open_bb_smrz,smrz_reason,pz_button_name,extra_data,open_bb_tj_tip,bb_tj_tip,open_tj_pz', $where);
        if (empty($app)) {
            return rs('抱歉，推广项目不存在');
        }

        $extra_data = j2a($app['extra_data']);
        $has_shudan = isset($extra_data['has_shudan']) ? $extra_data['has_shudan'] : 0;
        $pz_append = isset($extra_data['pz_append']) ? $extra_data['pz_append'] : 0;
        $shudan_def = '小说';
        if ($has_shudan == 3) {
            $shudan_def = '短剧';
        }
        $shudan_alias = $extra_data['shudan_alias'] ? $extra_data['shudan_alias'] : $shudan_def;
        $open_spdf = isset($extra_data['open_spdf']) ? $extra_data['open_spdf'] : 0;
        $open_apply_bb = isset($extra_data['open_apply_bb']) ? ints($extra_data['open_apply_bb']) : 0;

        //报备剩余次数s
        $bb_mr_xz = ints($extra_data['bb_mr_xz'] ?? 0);
        $bb_max_xz = ints($extra_data['bb_max_xz'] ?? 0);
        $bb_wrs = D('m_admin_tgbbdata')->get_white(['uid' => $uid], ['appid' => $app['id']]);
        if ($bb_wrs['code'] == 1) {
            //如果有白名单，以白名单为准
            $bb_white = $bb_wrs['data']['white_rules'];
            $bb_mr_xz = $bb_white['limit_day'];
            $bb_max_xz = $bb_white['limit_max'];
        }

        $bb_sy_num = null;
        $bb_sy_type = 1;
        $err_text = '';
        $tips = $app['name'];
        if ($bb_max_xz == 0) {
            $bb_max_xz_sy = -1;
        } else {
            $w = ['appid' => $app['id'], 'uid' => $uid, 'is_deleted' => 0, 'lx' => 1];
            $bb_nums = M()->count('x_tg_bb_data', $w);
            $bb_max_xz_sy = ints($bb_max_xz - $bb_nums);
            $bb_sy_num = $bb_max_xz_sy;//默认等于总次数剩余
            $bb_sy_type = 1;
            $err_text_all = $err_text = $open_apply_bb == 1 ? "<div>抱歉，您在{$tips}项目已累计报备{$bb_nums}次，已达到项目方要求，如需提高总报备次数，可前往工单系统申请。</div><div style='color: #848484; margin-top: 20px;'>步骤：在【项目咨询->报备咨询】中填写提高报备次数的申请说明，经项目方审核通过后，即可提高报备次数</div>" : "<div>抱歉，您在{$tips}项目已累计报备{$bb_nums}次，已达到项目方要求。</div>";
        }

        if ($bb_max_xz_sy == 0) {
            //已经达到总上限
            $bb_mr_xz_sy = 0;
            $bb_sy_num = 0;
            $bb_sy_type = 1;
        } else {
            if ($bb_mr_xz == 0) {
                $bb_mr_xz_sy = -1;
            } else {
                $w = ['appid' => $app['id'], 'uid' => $uid, 'created_at[>=]' => strtotime('today'), 'is_deleted' => 0, 'lx' => 1];
                $bb_nums2 = M()->count('x_tg_bb_data', $w);
                $bb_mr_xz_sy = ints($bb_mr_xz - $bb_nums2);
                $bb_sy_num = $bb_mr_xz_sy;//如果有天限制，优先天
                $bb_sy_type = 2;
                $err_text_mr = $err_text = $open_apply_bb == 1 ? "<div>抱歉，您今日提交报备的次数，已达到{$tips}项目方每天不超过{$bb_mr_xz}次的要求，如需提高报备次数，可前往工单系统申请。</div><div style='color: #848484; margin-top: 20px;'>步骤：在【项目咨询->报备咨询】中填写提高报备次数的申请说明，经项目方审核通过后，即可提高报备次数</div>" : "<div>抱歉，您今日提交报备的次数，已达到{$tips}项目方每天不超过{$bb_mr_xz}次的要求。</div>";
            }
        }

        if ($bb_max_xz_sy >= 0 && $bb_mr_xz_sy >= 0) {
            //如果天和总次数都有，优先最小的
            if ($bb_max_xz_sy > $bb_mr_xz_sy) {
                $bb_sy_type = 2;
                $bb_sy_num = $bb_mr_xz_sy;
                $err_text = $err_text_mr ?? $err_text;
            } else {
                $bb_sy_type = 1;
                $bb_sy_num = $bb_max_xz_sy;
                $err_text = $err_text_all ?? $err_text;
            }
        }

        //报备剩余次数e
        if ($open_spdf == 1) {
            if ($d['spdf'] == 1) {
                //过滤$extra_data['select_spdf'],取值为2或3的
                $select_spdf = array_filter($extra_data['select_spdf'], function ($v) {
                    return $v == 2 || $v == 3;
                });
            } else {
                $select_spdf = array_filter($extra_data['select_spdf'], function ($v) {
                    return $v == 1 || $v == 3;
                });
            }
            $app['bb_zd'] = implode(',', array_keys($select_spdf));
        }
        $x_tg_bb_types = m_apprw_bb::get_appbb_types(['id' => $app['bb_zd']]);
        //一个项目只能开启一个多条报备字段
        $open_multi = 0;
        foreach ($x_tg_bb_types as $k => &$x_tg_bb_type) {
            //是否支持书单模式
            if (in_arr($has_shudan, [1, 2, 3, 4])) {
                if (
                    ($appid == 553 && in_arr($x_tg_bb_type['bs'], ['zuozheming', 'idshuji', 'urlneirong'])) ||
                    ($appid == 748 && in_arr($x_tg_bb_type['bs'], ['xiaoshuolianjie'])) ||
                    ($appid == 799 && in_arr($x_tg_bb_type['bs'], ['shudan'])) ||
                    ($appid == 931 && in_arr($x_tg_bb_type['bs'], ['IDneirongyguLS9'])) ||
                    ($appid == m_sanfang_realnovel::APP_ID && in_arr($x_tg_bb_type['bs'], [m_sanfang_realnovel::BOOK_ID_FIELD, m_sanfang_realnovel::BOOK_NAME_FIELD])) ||
                    ($appid == m_sanfang_sereal::APP_ID && in_arr($x_tg_bb_type['bs'], [m_sanfang_sereal::DJ_ID_FIELD, m_sanfang_sereal::DJ_NAME_FIELD])) ||
                    ($appid == m_sanfang_kocbook::APP_ID && in_arr($x_tg_bb_type['bs'], [m_sanfang_kocbook::BOOK_ID_FIELD, m_sanfang_kocbook::BOOK_NAME_FIELD])) ||
                    ($appid == m_sanfang_kocplay::APP_ID && in_arr($x_tg_bb_type['bs'], [m_sanfang_kocplay::DJ_ID_FIELD, m_sanfang_kocplay::DJ_NAME_FIELD])) ||
                    ($appid == 917 && in_arr($x_tg_bb_type['bs'], ['idxiaoshuouR42SS', 'xiaoshuomingcheng62Lzbu'])) ||
                    ($appid == m_sanfang_lofter::APP_ID && in_arr($x_tg_bb_type['bs'], [m_sanfang_lofter::SHUDAN_URL_FIELD]))
                ) {
                    unset($x_tg_bb_types[$k]);
                    continue;
                }
                if (in_arr($x_tg_bb_type['bs'], [
                    x_config::SHUDAN_XS_NAME_FIELD_BS,
                    x_config::SHUDAN_DJ_NAME_FIELD_BS,
                    x_config::SHUDAN_URL_FIELD_BS,
                    x_config::SHUDAN_NR_ID])) {
                    unset($x_tg_bb_types[$k]);
                    continue;
                }
            }
            if ($open_multi == 1) {
                $x_tg_bb_type['open_multi'] = 0;
            }
            $x_tg_bb_type['select_arr'] = explode(",", $x_tg_bb_type['data_select']);
            if ((count($x_tg_bb_type['select_arr']) < 2) && $x_tg_bb_type['select_arr'][0] == "") {
                $x_tg_bb_type["select_arr"] = null;
            }
            if ($x_tg_bb_type['open_multi']) {
                $open_multi++;
            }
            $x_tg_bb_type['is_unique'] = (int)($x_tg_bb_type['id'] == $app['uniq_bb_zd']);
        }
        $x_tg_bb_types = m_apprw_bb::buildTree($x_tg_bb_types);
        $select_bb_types = m_apprw_bb::format_bb_types($x_tg_bb_types);
        $select_bb_zd = [];

        if ($from_token == 1) {
            $ysgx = RX('common', 'media_to_bbzd');
            $zdys = $ysgx['zdys'] ?? [];
        }
        $i = 1;
        foreach ($select_bb_types as $b) {
            if ($appid == 831 && $b['id'] == '1107') {
                unset($b['children'][1]);
            }

            // 分享报备媒体库处理
            if ($from_token == 1 && $b['ts_lx'] == 1) {
                $b['ts_lx'] = 0;
                if (issetx('children', $b) && is_array($b['children'])) {
                    foreach ($b['children'] as &$sencod_bbzd) {
                        if ($sencod_bbzd['ts_lx'] == 1) {
                            $sencod_bbzd['ts_lx'] = 0;
                        }
//                        if (issetx('children', $sencod_bbzd)) {
//                            foreach ($sencod_bbzd['children'] as $k => $third_bbzd) {
//                                if (in_arr($third_bbzd['bs'], explode(',', $zdys['xsmc'] ?? ''))) {
//                                    unset($sencod_bbzd['children'][$k]);
//                                }
//                            }
//                            $sencod_bbzd['children'] = array_merge($sencod_bbzd['children']);
//                        }
                    }
                }
            }
            $select_bb_zd[$i] = $b;
            $i++;
        }

        // 排序设置，根据后台设置的报备字段顺序，重新排序
        $new_bbzd = [];
        $old_bbzd = array_column($x_tg_bb_types, null, 'id');
        foreach (explode(',', $app['bb_zd']) as $v) {
            if (isset($old_bbzd[$v]) && $old_bbzd[$v]['ts_lx'] != 2) {
                $new_bbzd[] = $old_bbzd[$v];
            }
        }

        $tj_info_fileds = [];
        $open_zdy = in_arr($appid, [90, 972]) ? 1 : 0;
        $zdy = [];
        $select_arr_batch = [];
        if ($app['open_tj_pz'] == 1) {
            $tj_info_fileds = [];
            $tj_info_fileds_append = [];
            // 获取提交信息类型
            $tj_info_fileds_all = M()->select('x_tg_tj_info_filed', 'id,appid,name,type,data_type,data_select,help,tu_url,is_required,is_unique,open_multi,px,ht_lx', ['appid' => $appid, 'is_del' => 0], 'px desc,id asc');
            foreach ($tj_info_fileds_all as $k => &$item) {
                $item['type'] = ints($item['type']);
                $item['data_type'] = intval($item['data_type']);
                $item['is_show'] = 1;
                if ($item['ht_lx'] == 1) {
                    if ($item['data_type'] == self::DATA_TYPE_SELECT) {
                        if ($k == 0) {
                            $item['open_zdy'] = $open_zdy;
                        }
                        $select_arr = array_values(array_filter(explode(',', $item['data_select'])));
                        if (!empty($select_arr)) {
                            $select_help = explode('#', $item['help']);
                            foreach ($select_help as $k1 => $v1) {
                                $zdy[$k1][$item['id']]['help'] = $v1;
                            }
                        }
                        if ($open_zdy == 1 && $k == 0) {
                            $select_arr_batch = $select_arr;
                        }
                        $item['select_arr'] = $select_arr;
                        if (!empty($item['open_zdy'])) {
                            $item['help'] = '';
                        }
                    } else {
                        $item['select_arr'] = [];
                    }
                    if ($item['data_type'] == m_tgapp::DATA_TYPE_TJ_BATCH && $k == 0) {
                        //如果是回填批次
                        $open_zdy = 1;
                        $item['open_zdy'] = $open_zdy;
                        $item['data_select'] = '首次回填,补充回填';
                        $select_arr = array_values(array_filter(explode(',', $item['data_select'])));
                        $select_arr_batch = $select_arr;
                        $item['select_arr'] = $select_arr;
                    }

                    if ($k > 0 && $open_zdy == 1) {
                        //凭证第一次第二次处理
                        $this->tj_info_batch($appid, $item, $zdy, $select_arr_batch ?? []);
                    }
                }

                if ($item['type'] == m_tgapp::TJ_INFO_TYPE_VIDEO) {
                    $item['filesize_limit'] = 100;
                }

                $item['data_select'] = empty($item['select_arr']) ? false : $item['data_select'];
                // 转换是否必填为int类型
                $item['is_required'] = intval($item['is_required']);
                if ($item['ht_lx'] == 1) {
                    $tj_info_fileds[] = $item;
                } else {
                    $tj_info_fileds_append[] = $item;
                }
            }
        }
        $app_ext = M()->get('x_tg_app_ext', 'pl_bb_excel_url,pz_batch_tip', ['id' => $app['id']]);
        $pl_bb_excel_url = $app_ext["pl_bb_excel_url"];


        $bb_sm = html_entity_decode($app['bb_sm']);
        $bb_sm = str_replace('href="../', 'href="' . getym(true) . '/', $bb_sm);

        $data = [
            'appid' => ints($app['id']),
            'app_name' => $app['name'],
            'bb_zd' => $new_bbzd,
            'select_bb_zd' => $select_bb_zd,
            'bb_sm' => str_replace('<>', '＜＞', $bb_sm),
            'tj_info_fileds' => $tj_info_fileds,
            'tj_info_fileds_append' => $pz_append == 1 ? ($tj_info_fileds_append ?? []) : [],
            'pz_append' => $pz_append,//是否开启补充回填
            'zdy' => $zdy,
            'open_pl_bb' => !empty($pl_bb_excel_url) ? 1 : 0,
            'pl_bb_excel_url' => url2oss($pl_bb_excel_url ?: ''),
            'agree_bb_cxcnh' => 0,
            'bb_cxcnh' => '',
            'open_bb_smrz' => ints($app['open_bb_smrz']),
            'is_smrz' => 0,
            'pz_button_name' => $app['pz_button_name'],
            'team_bb_tip' => '报备须知：本页面用于项目报备信息填写，填写完成即代表您和邀请人进行了合作绑定，数据与结算请联系邀请人。',
            'ok_bb_tip' => '本页面只涉及项目报备，数据与结算请联系邀请人',
            'ok_pz_tip' => '本页面只涉及项目回填，数据与结算请联系邀请人',
            'has_shudan' => $has_shudan,
            'shudan_alias' => $shudan_alias,
            'open_apply_bb' => $open_apply_bb,//是否开启申请提交报备次数
            'open_apply_bb_err_text' => $err_text,//是否开启申请提交报备次数描述
            'bb_sy_num' => $bb_sy_num,//报备剩余次数
            'bb_sy_type' => $bb_sy_type,//报备剩余次数类型1总次数2每日
            'open_bb_tj_tip' => empty($app['bb_tj_tip']) ? 2 : ints($app['open_bb_tj_tip']),
            'bb_tj_tip' => str_replace('href="../', 'href="' . getym(true) . '/', html_entity_decode($app['bb_tj_tip'])),
            'open_spdf' => !empty($open_spdf) ? 1 : 0,
            'why_so_much_bb_info' => $from_token == 1 ? '' : '为什么报备比其他平台多？',
            'pz_batch_tip' => empty($app_ext['pz_batch_tip']) ? '
            <p>结算数据以官方反馈为准，介意勿做！</p>
             <p><br/></p>
            <p>操作说明：</p>
            <p>1、请在【腾讯文档】中新建表格；</p>
            <p>2、下载【凭证模板】将模板内容粘贴到腾讯文档的表格中；</p>
            <p>3、在腾讯文档中编辑凭证信息，图片需要粘贴在单元格中；</p>
            <p>4、下载腾讯文档的表格，将表格上传；</p>
            ' : html_entity_decode($app_ext['pz_batch_tip']),
            'pz_batch_file_max' => '200',
            'pz_batch_num_max' => '1000',
            'pz_import_button' => $app['open_tj_pz'] == 1 ? 1 : 0,
            'pz_share_button' => $app['open_tj_pz'] == 1 ? 1 : 0,
            'pz_num_max' => '20',
            'pz_ocr' => 0
        ];

        if (in_arr($appid, m_api_ocr_pz::OCR_APPID)) {
            //汽水音乐、抖小，需要走ocr，限制最大凭证提交次数
            $data['pz_import_button'] = 0;
            $data['pz_num_max'] = m_api_ocr_pz::PZ_NUM_MAX;
            $data['pz_ocr'] = 1;
        }
        $data['pz_num_max_text'] = '因项目方要求，单次最多仅支持提交' . $data['pz_num_max'] . '条数据，如需提交更多，可于本次提交后再次提交。';
        //分享邀请报备不提示诚信承诺函
        if ($from_token) {
            $app['bb_cxcnh'] = '';
        }
        if ($app['open_bb_cxcnh'] == 1 && !empty($app['bb_cxcnh'])) {
            $data['agree_bb_cxcnh'] = (int)M()->has('x_tg_app_cxcnh', ['appid' => $appid, 'uid' => $uid, 'content' => $app['bb_cxcnh']]);
            if (!$data['agree_bb_cxcnh']) {//如果已签约，无需返回
                $data['bb_cxcnh'] = html_entity_decode($app['bb_cxcnh']);
                $data['bb_cxcnh'] = str_replace('href="../', 'href="' . getym(true) . '/', $data['bb_cxcnh']);
                $data['bb_cxcnh'] = str_replace('<>', '＜＞', $data['bb_cxcnh']);
            }
        }
        if ($app['open_bb_smrz'] == 1 && !empty($app['smrz_reason'])) {
            //todo 企业子账号必须实名
            $smrz = m_user_main::user_has_smrz($user);
            $data['is_smrz'] = 1;
            if ($smrz['zt'] != m_smrz::ZT_OK) {//如果已实名认证，无需返回
                $data['is_smrz'] = 0;
                $data['smrz_reason'] = html_entity_decode($app['smrz_reason']);
                $data['smrz_reason'] = str_replace('href="../', 'href="' . getym(true) . '/', $data['smrz_reason']);
                $data['smrz_reason'] = str_replace('<>', '＜＞', $data['smrz_reason']);
            }
        }

        return rs('获取成功', 1, $data);
    }

    /**凭证按批次提交
     * @param $appid
     * @param $item
     * @param $select_arr
     * @param $zdy
     * @return array|string
     */
    public function tj_info_batch($appid, &$item, &$zdy, $select_arr)
    {
        $appid = ints($appid);
        $item = glwb($item);
        $zdy = glwb($zdy);
        $select_arr = glwb($select_arr);
        switch ($appid) {
            case 90:
                $item['is_show'] = 2;
                $select_arr_data = explode('#', $item['help']);
                if (isset($select_arr) && is_array($select_arr)) {
                    foreach ($select_arr as $kk => $t) {
                        $zdy[$kk][$item['id']]['help'] = $select_arr_data[$kk] ?? '';
                        if ($t != x_config::QUARK_QUANYI_SHARE_LINK) {
                            $zdy[$kk][$item['id']]['is_show'] = 1;
                            $zdy[$kk][$item['id']]['is_required'] = 1;
                        } else {
                            $zdy[$kk][$item['id']]['is_show'] = 2;
                            $zdy[$kk][$item['id']]['is_required'] = 0;
                        }
                    }
                }
                $item['help'] = '';
                break;
            case 972://懂车帝
                $item['is_show'] = 2;
                if (isset($select_arr) && is_array($select_arr)) {
                    foreach ($select_arr as $kk => $t) {
                        if ($kk == 0 && in_arr($item['id'], [1292, 1293, 1294])) {
                            //第一次回填
                            $zdy[$kk][$item['id']]['is_show'] = 1;
                            $zdy[$kk][$item['id']]['is_required'] = 1;
                        } else if ($kk == 1 && in_arr($item['id'], [1295, 1296])) {
                            //第二次回填
                            $zdy[$kk][$item['id']]['is_show'] = 1;
                            $zdy[$kk][$item['id']]['is_required'] = 1;
                        } else {
                            $zdy[$kk][$item['id']]['is_show'] = 2;
                            $zdy[$kk][$item['id']]['is_required'] = 0;
                        }
                    }
                }
                break;
            case 979://汽水音乐
                $item['is_show'] = 2;
                if (isset($select_arr) && is_array($select_arr)) {
                    foreach ($select_arr as $kk => $t) {
                        if ($kk == 0 && in_arr($item['id'], [1341, 1342, 1343, 1344, 1345, 1350, 1351, 1352])) {
                            //第一次回填
                            $zdy[$kk][$item['id']]['is_show'] = 1;
                            $zdy[$kk][$item['id']]['is_required'] = 1;
                        } else if ($kk == 1 && in_arr($item['id'], [1343, 1353])) {
                            //第二次回填
                            $zdy[$kk][$item['id']]['is_show'] = 1;
                            $zdy[$kk][$item['id']]['is_required'] = 1;
                        } else {
                            $zdy[$kk][$item['id']]['is_show'] = 2;
                            $zdy[$kk][$item['id']]['is_required'] = 0;
                        }
                    }
                }
                break;
            default:
                $item['is_show'] = 2;
                if (isset($select_arr) && is_array($select_arr)) {
                    foreach ($select_arr as $kk => $t) {
                        if ($kk == 0 && $item['px'] <= 75) {
                            //第一次回填
                            $zdy[$kk][$item['id']]['is_show'] = 1;
                            $zdy[$kk][$item['id']]['is_required'] = 1;
                        } else if ($kk == 1 && ($item['px'] <= 50 || $item['px'] > 75)) {
                            //第二次回填
                            $zdy[$kk][$item['id']]['is_show'] = 1;
                            $zdy[$kk][$item['id']]['is_required'] = 1;
                        } else {
                            $zdy[$kk][$item['id']]['is_show'] = 2;
                            $zdy[$kk][$item['id']]['is_required'] = 0;
                        }
                    }
                }
                break;
        }
        return $item;
    }

    /**
     * 默认佣金设置
     * @param $uid
     * @param $p_uid
     * @return array
     */
    public function get_default_yj_set_value($uid, $p_uid = 0)
    {
        $uid = ints($uid);
        $p_uid = ints($p_uid);
        $data = [
            'is_default' => 1,
            'default_xj_yj_bl' => 5,
            'default_xj_yj_money' => 0,
            'max_yj_bl' => RXX('x', 'max_yj_bl'),
        ];

        if (!$uid) {
            return rs('ok', 1, $data);
        }

        $p_uid = $uid == $p_uid ? 0 : $p_uid;

        $mark = 0;
        if ($p_uid) {
            $xj_yj_bl = M()->get('x_tg_user_xj_yj_bl', 'default_xj_yj_bl', ['p_uid' => $p_uid, 'uid' => $uid]);
            if (empty($xj_yj_bl)) {
                $mark = 1;
                $xj_yj_bl = M()->get('x_tg_user', 'default_xj_yj_bl', ['id' => ints($p_uid)]);
            }
        } else {
            $xj_yj_bl = M()->get('x_tg_user', 'default_xj_yj_bl', ['id' => ints($uid)]);
        }

        if (!empty($xj_yj_bl)) {
            $data['is_default'] = $mark ? 1 : 0;
            $data['default_xj_yj_bl'] = floatval($xj_yj_bl['default_xj_yj_bl']);
        }

        return rs('获取成功', 1, $data);
    }

    /**
     * 设置全局的佣金抽成
     *
     * @param array $d
     * @param int $uid
     * @return array
     */
    public function set_global_yj($d, $uid)
    {
        $d = glwb($d);
        $uid = ints($uid);
        $subUid = ints($d['subUid']);
        // 更新
        $check = [
            ['intzs', $uid, '登录超时!'],
            ['must', $d['value'] ?? '', '设置百分比不能为空'],
            ['num', $d['value'] ?? '', '设置百分比必须是数字'],
            ['min', [$d['value'] ?? '', 0], '设置百分比必须大于0'],
            ['preg', [$d['value'] ?? '', '/^[0-9]+(.[0-9]{1,2})?$/'], '设置百分比只能保留小数点后两位'],
        ];
        $ck = check($check);
        if ($ck !== true) {
            return rs($ck);
        }

        $queryUid = $uid;
        $hcm = $suo = '';
        if (BENDI != 1) {
            // redis上锁，防止频繁发送请求
            $hcm = 'update_global_app_rw_yj_' . $queryUid;
            $suo = lock($hcm, 60);
            if ($suo == 0) {
                return rs('操作过于频繁,请等待1分钟后重试!');
            }
        }

        if ($subUid) {
            $subUser = M()->get('x_tg_user', 'id,p_uid', ['id' => $subUid, 'status[!=]' => m_user_main::STATUS_LOGOFF]);
            if (empty($subUser)) {
                unlock($hcm, $suo);
                return rs('用户不存在或已注销');
            }

            if ($subUser['p_uid'] != $uid) {
                unlock($hcm, $suo);
                return rs('当前用户不是您的下级，请勿违规操作');
            }
            $queryUid = $subUid;
        }

        // 频率校验
        $pvre = D('m_setyj')->check_setpv($uid, $queryUid);
        if ($pvre['code'] != 1) {
            unlock($hcm, $suo);
            return rs($pvre['msg']);
        }

        $max_yj_bl = RXX('x', 'max_yj_bl');
        if ($d['value'] > $max_yj_bl) {
            // redis解锁
            unlock($hcm, $suo);
            return rs('抱歉，抽佣比例不能大于' . $max_yj_bl . '%');
        }

        if ($subUid) {
            $xjYjBL = M()->count('x_tg_user_xj_yj_bl', ['p_uid' => $uid, 'uid' => $queryUid]);
            if ($xjYjBL) {
                $res = M()->update('x_tg_user_xj_yj_bl', ['default_xj_yj_bl' => $d['value']], ['p_uid' => $uid, 'uid' => $queryUid], 1);
            } else {
                $res = M()->insert('x_tg_user_xj_yj_bl', ['default_xj_yj_bl' => $d['value'], 'p_uid' => $uid, 'uid' => $queryUid], 1);
            }
        } else {
            $res = M()->update('x_tg_user', ['default_xj_yj_bl' => $d['value'], 'default_xj_yj_money' => 0], ['id' => $queryUid], 1);
        }

        if ($res === false) {
            unlock($hcm, $suo);
            return rs('抱歉，您什么也没修改!');
        }

        // 佣金设置日志
        D('m_setyj')->insert_yj_log($uid, $queryUid);

        xlog(__METHOD__, "更新推广佣金成功，更新数据DATA：" . a2j($d), 'set_global_yj_' . $queryUid);
        // redis解锁
        unlock($hcm, $suo);
        return rs('修改成功', 1);
    }

    /**
     * 设置单个的任务佣金
     *
     * @param array $d
     * @param int $uid
     * @return array|false|mixed|string|string[]
     * @throws Exception
     */
    public function set_single_app_rw_yj($d, $uid)
    {
        $d = glwb($d);
        $uid = ints($uid);
        $rwId = ints($d['id']);
        $subUid = ints($d['subUid']);
        $check = [
            ['intzs', $uid, '抱歉，用户登录已过期'],
            ['must', $d['value'], '抽佣金额不能为空'],
            ['num', $d['value'] ?? '', '抽佣金额必须是数字'],
            ['min', [$d['value'] ?? '', 0], '抽佣金额必须大于0'],
            ['preg', [$d['value'] ?? '', '/^[0-9]+(.[0-9]{1,2})?$/'], '抽佣金额只能保留小数点后两位'],
            ['intzs', $rwId, '任务ID不能为空'],
        ];
        $ck = check($check);
        if ($ck !== true) {
            return rs($ck);
        }

        $queryUid = $uid;
        $hcm = $suo = '';
        if (BENDI != 1) {
            // redis上锁，防止频繁发送请求
            $hcm = 'update_single_app_rw_yj_' . $queryUid . '_' . $rwId;
            $suo = lock($hcm, 60);
            if ($suo == 0) {
                return rs('抱歉，操作频繁，请稍后再试!');
            }
        }

        $rw = get_app_rw(0, $rwId);
        if (empty($rw)) {
            unlock($hcm, $suo);
            return rs('任务不存在或已删除');
        }

        if ($rw['open_cy'] != 1) {
            unlock($hcm, $suo);
            return rs('抱歉，当前任务为特殊任务，不允许设置我的收益');
        }

        if ($subUid) {
            $subUser = M()->get('x_tg_user', 'id,p_uid', ['id' => $subUid, 'status[!=]' => m_user_main::STATUS_LOGOFF]);
            if (empty($subUser)) {
                unlock($hcm, $suo);
                return rs('用户不存在或已注销');
            }

            if ($subUser['p_uid'] != $uid) {
                unlock($hcm, $suo);
                return rs('当前用户不是您的下级，请勿违规操作');
            }
            $queryUid = $subUid;
        }

        // 频率校验
        $pvre = D('m_setyj')->check_setpv($uid, $queryUid, $rwId);
        if ($pvre['code'] != 1) {
            unlock($hcm, $suo);
            return rs($pvre['msg']);
        }

        $yj_data = get_user_app_yj($uid, $rwId);
        if ($yj_data['code'] != 1) {
            unlock($hcm, $suo);
            return rs($yj_data['msg']);
        }
        $userAppYJ = reset($yj_data['data']);

        if (empty($userAppYJ)) {
            unlock($hcm, $suo);
            return rs('抱歉，任务已删除，请刷新页面');
        }

        if ($d['value'] > $userAppYJ['self_price']) {
            unlock($hcm, $suo);
            return rs("抱歉，抽佣金额不能大于任务价格");
        }

        // 抽取下级佣金最高不超过 self_price*$max_yj_bl/100
        $max_yj_bl = RXX('x', 'max_yj_bl');
        $percent = float_mul(float_div($d['value'], $userAppYJ['self_price'], 4), 100);
        if ($percent > $max_yj_bl) {
            unlock($hcm, $suo);
            return rs("抱歉，抽佣金额不能超过任务价格的{$max_yj_bl}%，当前任务最大抽佣金额" . float_mul($userAppYJ['self_price'], 0.2));
        }

        if (float_sub($userAppYJ['self_price'], $d['value']) > $userAppYJ['price']) {
            unlock($hcm, $suo);
            return rs('尊敬的渠道，抽佣后的下级金额不能大于平台价，当前平台价格：' . $userAppYJ['price']);
        }

        $w = ['uid' => $queryUid, 'rw_id' => $rwId, 'p_uid' => $uid];
        $appYjExist = M()->get('x_tg_app_yj', '1', $w);
        if ($appYjExist) {
            $res = M()->update('x_tg_app_yj', ['yj_value' => $d['value'], 'is_fixed' => 1], $w, 1);
        } else {
            $res = M()->insert('x_tg_app_yj', ['uid' => $queryUid, 'rw_id' => $rwId, 'p_uid' => $uid, 'yj_value' => $d['value'], 'is_fixed' => 1], 1);
        }

        if ($res === false) {
            unlock($hcm, $suo);
            return rs('抱歉，修改失败，请稍后重试');
        }

        // 佣金设置日志
        D('m_setyj')->insert_yj_log($uid, $queryUid, $rwId);
        // redis解锁
        unlock($hcm, $suo);
        return rs('恭喜，修改成功', 1);
    }

    /**
     * 取消单个任务的固定佣金设置
     *
     * @param array $d
     * @param int $uid
     */
    public function cancel_single_app_rw_yj($d, $uid)
    {
        $d = glwb($d);
        $uid = ints($uid);
        $rwId = ints($d['id']);
        $subUid = ints($d['subUid']);
        $check = [
            ['intzs', $uid, '抱歉，用户登录已过期'],
            ['intzs', $rwId, '任务ID不能为空'],
        ];
        $ck = check($check);
        if ($ck !== true) {
            return rs($ck);
        }

        $queryUid = $uid;
        // redis上锁，防止频繁发送请求
        $hcm = 'cancel_single_app_rw_yj_' . $queryUid . '_' . $rwId;
        $suo = lock($hcm, 60);
        if ($suo == 0) {
            return rs('抱歉，操作频繁，请稍后再试!');
        }

        if ($subUid) {
            $subUser = M()->get('x_tg_user', 'id,p_uid', ['id' => $subUid, 'status[!=]' => m_user_main::STATUS_LOGOFF]);
            if (empty($subUser)) {
                unlock($hcm, $suo);
                return rs('用户不存在或已注销');
            }

            if ($subUser['p_uid'] != $uid) {
                unlock($hcm, $suo);
                return rs('当前用户不是您的下级，请勿违规操作');
            }
            $queryUid = $subUid;
        }

        $w = ['uid' => $queryUid, 'rw_id' => $rwId, 'p_uid' => $uid];
        $appYjExist = M()->has('x_tg_app_yj', $w);
        if ($appYjExist) {
            M()->delete('x_tg_app_yj', ['p_uid' => $uid, 'uid' => $queryUid, 'rw_id' => $rwId], 1);
        }

        // redis解锁
        unlock($hcm, $suo);
        return rs('恭喜，修改成功', 1);
    }

    /**
     * 查询产品的佣金
     * @param int $uid
     */
    public function get_app_yj($d, $uid)
    {
        $uid = ints($uid);
        $d = glwb($d);
        $subUid = ints($d['subUid']);
        $check = [
            ['intzs', $uid, '抱歉，用户登录已过期'],
        ];
        $ck = check($check);
        if ($ck !== true) {
            return rs($ck);
        }

        $queryUid = $uid;
        if ($subUid) {
            $subUser = M()->get('x_tg_user', 'id,p_uid', ['id' => $subUid]);
            if (empty($subUser)) {
                return rs('用户不存在');
            }

            if ($subUser['p_uid'] != $uid) {
                return rs('当前用户不是您的下级，请勿违规操作');
            }
            $queryUid = $subUid;
        }
        $sort = "desc";
        $page = ints($d['page'], 1);
        $limit = ints($d['limit'], 500);

        $defaultYJRes = $this->get_default_yj_set_value($queryUid, $uid);
        if ($defaultYJRes['code'] == 1) {
            $defaultYJ = $defaultYJRes['data'];
        }

        $ret = ['list' => [], 'count' => 0, 'default_yj_setting' => $defaultYJ ?? []];
        if (!empty($d["sort"])) {
            if (!in_arrlb($d["sort"], ["asc", "desc"])) {
                return rs("排序传参错误");
            }
            $sort = $d["sort"];
        }

        $where = ['status' => 1];
        if (!empty($d["keyword"])) {
            $where["name[~]"] = $d["keyword"];
        }
        if (!empty($d["category"]) && $d["category"] != 1) {
            if (!M()->has("x_nav", ["status" => 1, "id" => $d["category"]])) {
                return rs("该分类不存在");
            }
            $nav_appids = M()->select("x_nav_app", "distinct(appid) as appid", ["nav_id" => $d["category"], 'lx' => 1, 'is_del' => 0], "sort desc");
            $nav_appids = array_column($nav_appids, "appid");
            $where["id"] = $nav_appids;
        }

        $appRes = M()->s_page('x_tg_app', 'id,name,image,position', $where, "position {$sort}", $limit, $page);
        if (empty($appRes['data'])) {
            return rs('获取成功', 1, $ret);
        }

        $appid = array_column($appRes, 'id');
        $yj_data = get_user_app_yj($uid, get_rw_id_by_appid($appid), $subUid);
        if ($yj_data['code'] != 1) {
            return rs($yj_data['msg']);
        }

        $userAppYJ = $yj_data['data'];
        $YJData = arr_zu($userAppYJ, 'appid');
        $data = [];
        foreach ($appRes['data'] as $app) {
            $_data = [
                'appid' => $app['id'],
                'app_logo' => url2oss($app['image']),
                'app_name' => $app['name'],
                'rw_list' => $YJData[$app['id']]
            ];
            $data[] = $_data;
        }

        $ret['list'] = $data;
        $ret['count'] = $appRes['count'];
        $ret["category"] = D('m_api')->get_index_cate();
        return rs('获取成功', 1, $ret);
    }

    //删除项目总金额（缓存）
    public static function clear_app_all_money()
    {
        $key = RX("redis_key", "APP_ALL_MONEY");
        $redis = new redisx();
        $redis->del($key);
        return true;
    }

    /**
     * 获取项目总金额（缓存）
     * @return mixed
     * @throws Exception
     */
    public static function get_app_all_money()
    {
        $lishi = M()->get('x_site', 'value', ['bs' => 'tg_app_dczgjs'], '', 3600);
        $data = j2a($lishi['value']);
        return array_column($data, 'all_money', "appid");
//
//        $key = RX("redis_key", "APP_ALL_MONEY2");
//        $redis = new redisx();
//        $list = $redis->hgetall($key);
//        if (empty($list)) {
//            $data = M()->select("x_tg_detail", "max(total_money) as all_money,appid", "status=1 group by appid");
//            $appMoneyMap = array_column($data, 'all_money', "appid");
//            foreach ($appMoneyMap as $appid => $money) {
//                $redis->hset($key, "$appid", $money);
//                $redis->expire($key, 86400);
//            }
//            $list = $appMoneyMap;
//        }
//        return $list;
    }

    /**
     * 获取项目总金额（缓存）
     * @return array
     * @throws Exception
     */
    public static function get_app_all_bb_counts()
    {
        $list = M()->get('x_site', 'value', ['bs' => 'app_all_bb_counts', 'channel_id' => 0], 'id desc', 7200);
        return j2a($list['value']);

//        $key = RX("redis_key", "APP_ALL_BB_COUNTS");
//        $redis = new redisx();
//        $list = $redis->hgetall($key);
//        if (empty($list)) {
//            $bbCountRes = M()->select('x_tg_bb_data', "count(1) as num,1 as has_status_1,appid", 'is_deleted=0 group by appid');
//            $bbCountsMap = array_column($bbCountRes, null, 'appid');
//            foreach ($bbCountsMap as $val) {
//                $redis->hset($key, "{$val['appid']}", a2j($val));
//            }
//            $redis->expire($key, 86400);
//            $list = $bbCountsMap;
//        }
//        return $list;
    }

    /**
     * @Desc:获取推荐项目
     * @param $u
     * @param $d
     * @return array
     * @throws Exception
     * @author: chenchen
     * @Time: 2023/11/10 14:22
     */
    public function get_tj_app($u, $d)
    {
        $uid = ints($u['uid']);
        if (!$uid) {
            return rs("该用户未登录");
        }
        $d = glwb($d);
        $user_ext = M()->get('x_tg_user_ext', 'id,open_tj_msg', ['id' => $uid]);
        if (!empty($user_ext) && $user_ext['open_tj_msg'] != 1) {
            return success();
        }

        $re = D('m_tgapp')->get_u_tg_apps(['status' => 1, 'self_price_order' => 'desc', 'cate_id' => 1, 'is_smart_tj' => 1, 'limit' => 20, 'is_ios_audit' => $d['is_ios_audit']], $u);
        return success($re['data']['app']);
    }

    /**
     * 推荐项目
     * @param $u
     * @param $d
     * @return array
     * @throws Exception
     */
    public function get_tj_app_msg($u, $d)
    {
        $uid = ints($u['uid']);
        if (!$uid) {
            return rs('抱歉，用户登录已过期');
        }
        $d = glwb($d);
        $open_tj_msg = (int)M()->get_field('x_tg_user_ext', 'open_tj_msg', ['id' => $uid]);
        if ($open_tj_msg != 1) {
            return success();
        }

        $tj_app_msg = M()->get("x_tj_app_msg", "id,remake,appid", ["uid" => $uid, "status" => 2], "id asc");

        if (empty($tj_app_msg)) {
            return success();
        }


        $appid = $tj_app_msg['appid'];
        $re = D('m_tgapp')->get_u_tg_apps(['app_ids' => [$appid], 'cate_id' => 1, 'is_ios_audit' => $d['is_ios_audit'], 'show_money' => 1], $u);
        $data = $re["data"]["app"][0] ?? [];
        if (!$data) {
            $res = M()->update('x_tj_app_msg', ['status' => 1], ["id" => $tj_app_msg["id"], 'uid' => $uid], 1);
            if (!$res) {
                return rs();
            }
        }

        return rs('ok', 1, [
            'data' => $data,
            'pop_id' => $tj_app_msg["id"]
        ]);

    }

    //首页项目推荐缓存数据结构
    private $index_app_tj = [
        'status' => 0,     //关闭状态，1-关闭3天，2-长期关闭，3-可推荐
        'close_expiry' => '',   //关闭3天的过期时间
        'tj_tj' => 0,   //推荐条件，1-填写做单类型，2-已报备
        'appid' => [],   //推荐项目
        'except_appid' => [],   //排除项目
    ];

    //验证是否展示首页推荐
    private function _check_index_tj_app($u)
    {
        $u = glwb($u);
        $uid = ints($u["uid"]);
        if (!$uid) {
            return rs('抱歉，用户登录已过期');
        }
        //是否关闭
        $hc_key = RN('INDEX_APP_TJ') . ':' . $uid;
        $index_app_tj = S($hc_key);
        if (empty($index_app_tj)) {
            $index_app_tj = $this->index_app_tj;
        } else {
            if ($index_app_tj['status'] == 1 && $index_app_tj['close_expiry'] > time()) {
                return rs('首页推荐已关闭3天');
            } elseif ($index_app_tj['status'] == 2) {
                return rs('首页推荐已关闭');
            }
        }

        //查询表内是否关闭
        $tj = M()->get_field('x_tg_user_ext', 'open_index_tj', ["id" => $uid]);
        if ($tj == 2) {
            $index_app_tj['status'] = 2;
            S($hc_key, $index_app_tj, 3600);
            return rs('首页推荐已关闭');
        }
        //条件
        //做过单的或填写过做单类型的
        $is_tx = M()->get('x_tg_user_info', 'bc_zdlx', ['id' => $uid]);
        if (!empty($is_tx['bc_zdlx'])) {
            $index_app_tj['status'] = 3;
            $index_app_tj['tj_tj'] = 1;
            S($hc_key, $index_app_tj, 3600);
            return rs('已填写做单类型可推荐', 1);
        }
        $is_bb = M()->has('x_tg_bb_data', ['uid' => $uid, 'is_deleted' => 0]);
        if ($is_bb) {
            $index_app_tj['status'] = 3;
            $index_app_tj['tj_tj'] = 2;
            S($hc_key, $index_app_tj, 3600);
            return rs('已报备可推荐', 1);
        }
        return rs('不可推荐');
    }

    /**
     * 关闭首页项目推荐
     * @param $u
     * @param $d
     * @return array
     */
    public function close_index_tj_app($u, $d)
    {
        $u = glwb($u);
        $uid = ints($u["uid"]);
        $d = glwb($d);
        $type = ints($d["type"], 1);
        if (!$uid) {
            return rs('抱歉，用户登录已过期');
        }
        if (!in_arr($type, [1, 2, 3])) {
            return rs('抱歉，请选择正确的关闭方式');
        }
        $hc_key = RN('INDEX_APP_TJ') . ':' . $uid;
        $index_app_tj = S($hc_key);
        if (empty($index_app_tj)) {
            $index_app_tj = $this->index_app_tj;
        } else {
            if ($type == 1 && $index_app_tj['status'] == 1 && $index_app_tj['close_expiry'] > time()) {
                return rs('首页推荐已关闭3天');
            } elseif ($type == $index_app_tj['status']) {
                return rs('抱歉状态未变化');
            }
        }
        //长期关闭
        if (in_arr($type, [2, 3])) {
            $rs = M()->update('x_tg_user_ext', ['open_index_tj' => $type], ["id" => $uid], 1);
            if ($rs > 0) {
                $index_app_tj['status'] = $type;
                S($hc_key, $index_app_tj, 3600);
                if ($type == 2) {
                    $msg = '首页推荐已关闭';
                } else {
                    $msg = '首页推荐已打开';
                }
                return rs($msg, 1);
            }
        } elseif ($type == 1) {
            //关闭3天
            $index_app_tj['status'] = 1;
            $index_app_tj['close_expiry'] = time() + 86400 * 3;
            S($hc_key, $index_app_tj, 86400 * 3);
            return rs('首页推荐已关闭3天', 1);
        }
        return rs('抱歉，操作失败，请重试');
    }

    /**
     * 推荐项目
     * @param $u
     * @param $d
     * @return array
     * @throws Exception
     */
    public function get_index_tj_app($u, $d)
    {
        $u = glwb($u);
        $uid = ints($u['uid']);
        if (!$uid) {
            return rs('抱歉，用户登录已过期');
        }
        $d = glwb($d);
        if ($d['is_ios_audit'] == 1) {
            return rs('IOS该用户不显示首页推荐', 1, []);
        }
        //验证是否推荐
        $is_tj = $this->_check_index_tj_app($u);
        if ($is_tj['code'] != 1) {
            return rs('该用户不显示首页推荐', 1, []);
        }
        $num = ints($d['num'], 2);
        $tj_app_num = 12;//推荐12条
        if ($num > $tj_app_num) {
            //最多12条
            $num = $tj_app_num;
        }

        //获取推荐appid，缓存1小时
        $hc_key = RN('INDEX_APP_TJ') . ':' . $uid . '_' . $tj_app_num . $num;
        $index_app_tj = S($hc_key);

        if (empty($index_app_tj) || empty($index_app_tj['appid'])) {
            //初始化结构，实际上走不到，容灾
            if (empty($index_app_tj)) {
                $index_app_tj = $this->index_app_tj;
            }
            if (empty($index_app_tj['appid'])) {
                //查询所有项目+缓存
                $hc_key_all = RN('INDEX_ALL_APP_EXCEPT_WP');
                $all = S($hc_key_all);
                if (empty($all)) {
                    $w = ['status' => 1, 'lx' => m_tgapp::TG_APP_LX_NORMAL];
                    //删除网盘项目
                    $wp_app = M()->select('x_nav_app', 'appid', ['nav_id' => 7, 'lx' => 1, 'is_del' => 0]);
                    $wp_appids = array_column($wp_app, 'appid');
                    $w['id[!=]'] = $wp_appids;
                    //查询除网盘之外其他项目
                    $all = M()->select('x_tg_app', 'id,bc_zdlxs', $w, 'id desc');
                    if (empty($all)) {
                        return rs('暂无推荐数据', 1, []);
                    }
                    S($hc_key_all, $all, 86400 * 30);
                }
                //过滤用户项目
                $allow_id = $except_id = [];
//                //1，区分用户渠道
//                if ($u['channel_id']) {
//                    $channel = M()->get('x_third_channel', 'id,show_app,hide_app', ['id' => $u['channel_id']]);
//                    if (!empty($channel)) {
//                        $showApp = array_x(explode(',', $channel['show_app']));
//                        $hideApp = array_x(explode(',', $channel['hide_app']));
//                        if (!empty($showApp) && !empty($hideApp)) {
//                            $allow_id = $showApp;
//                            $except_id = $hideApp;
//                        } else if (!empty($showApp)) {
//                            $allow_id = $showApp;
//                        } else if (!empty($hideApp)) {
//                            $except_id = $hideApp;
//                        }
//                    }
//                }

                //2，推广黑名单
                $blackAppIds = M()->select("x_tg_app_black_list", "appid", ["tg_uid" => $uid]);
                $blackAppIds = array_column($blackAppIds, "appid");
                if (!empty($blackAppIds)) {
                    $except_id = array_merge($blackAppIds);
                }
                //处理过滤数据
//                if (!empty($allow_id)){
//                    $all = $this->_filter_array($all,$allow_id);
//                    dd($all);
//                }
                if (!empty($except_id)) {
                    $all = $this->_filter_array($all, $except_id, 'id', true);
                }
                if (empty($all)) {
                    return rs('暂无推荐数据', 1, []);
                }
                $all_appid = array_column($all, 'id');

                //关联项目推荐
                $bb = M()->select('x_tg_bb_data', 'appid', ['uid' => $uid, 'is_deleted' => 0]);
                $bb_apps = [];
                if (!empty($bb)) {
                    $bb_appid = array_column($bb, 'appid');
                    //查询推荐项目
                    $rel = M()->select('x_tg_app_ext', 'id,relevance_appid', ['id' => $bb_appid, 'relevance_appid[!=]' => ['', '["null]']]);
                    if (!empty($rel)) {
                        foreach ($rel as $item) {
                            $relevance_appid = j2a($item['relevance_appid']);
                            foreach ($relevance_appid as $v) {
                                if (in_arr($v, $all_appid)) {
                                    $bb_apps[$v] = ['bb_appid' => $item['id']];
                                }
                            }
                        }
                    }
                }

                //做单类型推荐
                $user_info = M()->get('x_tg_user_info', 'bc_zdlx', ['id' => $uid]);
                $zdlx_apps = [];
                if (!empty($user_info) && is_array(j2a($user_info['bc_zdlx']))) {
                    foreach ($all as $app) {
                        $intersection = array_intersect(j2a($app['bc_zdlxs']), j2a($user_info['bc_zdlx']));
                        if (!empty($intersection)) {
                            $zdlx_apps[$app['id']] = ['zdlx' => $intersection[array_rand($intersection)]];
                        }
                    }
                }
                $apps = $bb_apps + $zdlx_apps;

                //推荐项目不足10条补全
                $app_count = count($apps);
                if ($app_count < $tj_app_num) {
                    $cha = bcsub($tj_app_num, $app_count);
                    $bc_data_key = array_rand(array_column($all, 'id'), $cha);
                    if (!empty($bc_data_key)) {
                        if ($cha == 1) {
                            $bc_data_key = [$bc_data_key];
                        }
                        if (is_array($bc_data_key)) {
                            foreach ($bc_data_key as $key) {
                                $bc_data[$all[$key]['id']] = ['bc_appid' => $all[$key]['id']];
                            }
                        }
                    }
                    if (!empty($bc_data)) {
                        $apps = $apps + $bc_data;
                    }
                }
                //缓存所有apps
                $index_app_tj['appid'] = $apps;
                S($hc_key, $index_app_tj, 3600);
            } else {
                $apps = $index_app_tj['appid'];
            }
        } else {
            $apps = $index_app_tj['appid'];
        }
        $index_app_tj['except_appid'] = $index_app_tj['except_appid'] ?? [];
        if (!empty($index_app_tj['except_appid'])) {
            //与历史不同的app
            $appids = array_diff(array_keys($index_app_tj['appid'] ?? []), $index_app_tj['except_appid']);
            if (count($appids) <= $num) {
                //如果到达最高数量，不同数量小于取出数量了。保留最后3$num个，与当前
                $index_app_tj['except_appid'] = array_slice($index_app_tj['except_appid'], -$num);
                S($hc_key, $index_app_tj, 3600);
            }
            $new = [];
            foreach ($index_app_tj['appid'] as $k => $datum) {
                if (in_arr($k, $appids)) {
                    $new[$k] = $datum;
                }
            }
            if (count($new) >= $num) {
                $apps = $new;
            }
        } else {
            $apps = $index_app_tj['appid'];
        }

        //每次取2条
        $appid = array_rand($apps, $num);
        $ly_appid = $ly_zdlx = $ly_bc = [];
        foreach ($appid as $v) {
            if (!empty($apps[$v]['bb_appid'])) {
                $ly_appid[$v] = $apps[$v]['bb_appid'];
            } elseif (!empty($apps[$v]['zdlx'])) {
                $ly_zdlx[$v] = $apps[$v]['zdlx'];
            } elseif (!empty($apps[$v]['bc_appid'])) {
                $ly_bc[$v] = $apps[$v]['bc_appid'];
            }
        }
        $index_app_tj['except_appid'] = array_x(array_merge($index_app_tj['except_appid'], $appid));
        S($hc_key, $index_app_tj, 3600);
        $re = D('m_tgapp')->get_u_tg_apps(["app_ids" => $appid, "cate_id" => 1, 'is_ios_audit' => $d['is_ios_audit'], 'show_money' => 1], $u);
        $data = $re["data"]["app"] ?? [];
        if (!empty($data)) {
            //查询依据来源
            if (!empty($ly_appid)) {
                $ly_app_info = M()->select('x_tg_app', 'id,name', ['id' => $ly_appid]);
                $ly_app_info = array_column($ly_app_info, 'name', 'id');
            }
            if (!empty($ly_zdlx)) {
                $ly_zdlx_info = M()->get('x_site', 'value', ['bs' => 'zdlx'], '', 3600);
                $ly_zdlx_info = j2a($ly_zdlx_info['value']);
            }
            if (!empty($ly_bc)) {
                $ly_bc_info = M()->select('x_tg_app', 'id,name', ['id' => $ly_bc]);
                $ly_bc_info = array_column($ly_bc_info, 'name', 'id');
            }
            foreach ($data as &$value) {
                if (!empty($ly_app_info) && $ly_appid[$value['app_id']]) {
                    $value['tjly_msg'] = '您做过的“<span style="color:#0d53ff;">' . $ly_app_info[$ly_appid[$value['app_id']]] . '</span>”同类';
                } elseif (!empty($ly_zdlx_info) && $ly_zdlx[$value['app_id']]) {
                    $value['tjly_msg'] = '适合您的做单类型：<span style="color:#0d53ff;">' . $ly_zdlx_info[$ly_zdlx[$value['app_id']]] . '</span>';
                } elseif (!empty($ly_bc_info) && $ly_bc[$value['app_id']]) {
                    $value['tjly_msg'] = '系统为您推荐';
                }
            }
        } else {
            return rs('暂无数据推荐', 1, []);
        }
        return rs('ok', 1, $data);
    }

    private function _filter_array($array, $ids, $field = 'id', $exclude = false)
    {
        return array_filter($array, function ($item) use ($ids, $field, $exclude) {
            $condition = in_array($item[$field], $ids);
            return $exclude ? !$condition : $condition;
        });
    }

    //获取app基础信息
    public function get_app_base_info($u, $d)
    {
        $d = glwb($d);
        $uid = ints($u['uid']);
        $appid = ints($d['appid']);
        // 更新
        $check = [
            ['intzs', $uid, '抱歉，用户登录已过期!'],
            ['intzs', $appid, '抱歉，请先选择项目!'],
        ];
        $ck = check($check);
        if ($ck !== true) {
            return rs($ck);
        }

        $app = M()->key('get_app_base_info_' . $appid)->get('x_tg_app', 'id,name,status,image', ['id' => $appid], '', '60');
        if (empty($app)) {
            return rs('推广对象不存在');
        } else {
            $app['image'] = url2oss($app['image']);
        }
        return rs('ok', 1, $app);
    }

    /**根据id 查询app列表
     *
     * @param $ids
     *
     * @return array
     */
    public function get_apps($ids)
    {
        $ids = inta($ids);
        if (!$ids) {
            return rs('ok', 1, []);
        }
        $list = M()->select('x_tg_app', 'id,name', ['id' => $ids]);
        return rs('ok', 1, array_column($list, null, 'id'));
    }

    //获取夸克项目原名称，appid=469
    public function get_kk_ymc($u, $d)
    {
        $u = glwb($u);
        $d = glwb($d);
        //无需登录
        $page = ints($d['page'], 1);
        $limit = min(ints($d['limit'], 20), 20);
        $limit = 100;
        $pz = M()->s_page('x_tg_qt_conf', 'id,name,px', ['is_del' => 0, 'status' => 1, 'lx' => 1], 'px desc,id asc', $limit, $page);
        return rs('ok', 1, $pz);
    }

    /**
     * 检测项目上线后是否禁止报备和项目下线后是否允许报备
     */
    private function check_bb($app)
    {
        $extra_data = j2a($app['extra_data']);
        // 项目下架后是否允许报备
        if ($app['status'] == 2 && (!issetx('down_allow_bb', $extra_data) || $extra_data['down_allow_bb'] != 1)) {
            return rs('抱歉，当前项目已下架!');
        }

        // 项目上架后禁止报备
        if ($app['status'] == 1 && issetx('up_forbid_bb', $extra_data) && $extra_data['up_forbid_bb'] == 1) {
            $forbid_reason = '抱歉，' . ($extra_data['jzbb_reason'] ?? '当前项目禁止报备!');
            if (
                (!issetx('jzbb_s_date', $extra_data) && !issetx('jzbb_e_date', $extra_data)) ||
                (!issetx('jzbb_s_date', $extra_data) && issetx('jzbb_e_date', $extra_data) && time() <= strtotime($extra_data['jzbb_e_date'])) ||
                (issetx('jzbb_s_date', $extra_data) && !issetx('jzbb_e_date', $extra_data) && time() >= strtotime($extra_data['jzbb_s_date'])) ||
                (issetx('jzbb_s_date', $extra_data) && issetx('jzbb_e_date', $extra_data) && time() >= strtotime($extra_data['jzbb_s_date']) && time() <= strtotime($extra_data['jzbb_e_date']))
            ) {
                return rs($forbid_reason);
            }
        }

        return true;
    }

    /**
     * 检测项目下线是否允许提交凭证和上线是否禁止提交凭证
     */
    private function check_pz($app)
    {
        $extra_data = j2a($app['extra_data']);
        // 项目下架后是否允许提交凭证
        if ($app['status'] == 2 && (!issetx('down_allow_pz', $extra_data) || $extra_data['down_allow_pz'] != 1)) {
            return rs('抱歉，当前项目已下架');
        }

        // 项目上架后禁止提交凭证
        if ($app['status'] == 1 && issetx('up_forbid_pz', $extra_data) && $extra_data['up_forbid_pz'] == 1) {
            $forbid_reason = '抱歉，' . ($extra_data['jzpz_reason'] ?? '当前项目禁止提交凭证!');
            if (
                (!issetx('jzpz_s_date', $extra_data) && !issetx('jzpz_e_date', $extra_data)) ||
                (!issetx('jzpz_s_date', $extra_data) && issetx('jzpz_e_date', $extra_data) && time() <= strtotime($extra_data['jzpz_e_date'])) ||
                (issetx('jzpz_s_date', $extra_data) && !issetx('jzpz_e_date', $extra_data) && time() >= strtotime($extra_data['jzpz_s_date'])) ||
                (issetx('jzpz_s_date', $extra_data) && issetx('jzpz_e_date', $extra_data) && time() >= strtotime($extra_data['jzpz_s_date']) && time() <= strtotime($extra_data['jzpz_e_date']))
            ) {
                return rs($forbid_reason);
            }
        }

        return true;
    }

    //风控弹窗
    public function fengkong_popup($u, $d)
    {
        $u = glwb($u);
        $d = glwb($d);
        $uid = ints($u['uid']);
        $appid = ints($d['appid']);
        // 更新
        $check = [
            ['intzs', $uid, '抱歉，用户登录已过期!'],
            ['intzs', $appid, '抱歉，请先选择项目!'],
        ];
        $ck = check($check);
        if ($ck !== true) {
            return rs($ck);
        }
        $app = M()->get('x_tg_app', 'id,name', ['id' => $appid]);
        if (empty($app)) {
            return rs('推广对象不存在');
        }
        //先查项目有没有风控tips
        $exist_fk_popup = M()->get('x_tg_user_tips', 'has_new_bb_fk_popup', ['uid' => $uid, 'appid' => $appid]);
        if ($exist_fk_popup['has_new_bb_fk_popup'] == 0) {
            return rs('ok', 1, ['content' => '', 'status' => 4]);
        }
        //查询风控报备数据
        $bb_data = $this->_fk_bb($u, $d);
        if (empty($bb_data)) {
            //更新tips
            m_tguser::update_or_insert_tips($uid, $appid, 'fk_popup', 0);
            return rs('ok', 1, ['content' => '', 'status' => 4]);
        }
//        foreach ($bb_data as $key => &$item) {
//            // 风控中报备信息风控申诉状态处理
//            $postData = j2a($item['post_data']);
//            if (empty($postData)) {
//                continue;
//            }
//            $bbzdData = j2a($item['bb_data']);
//            $bbzdData = array_column($bbzdData, null, 'id');
//            $uniq_bb = $bbzdData[$item['uniq_bb_zd']] ?? '';
//            $bs = $uniq_bb['bs'];
//            $fieldsMap = [];
//            $fieldsMap[] = ['name' => '所属项目', 'val' => $app[$item['appid']] ?? '已删除', 'show_type' => 'txt'];
//            foreach ($postData as $k => $v) {
//                if ($k == $bs) {
//                    $fieldsMap[] = ['name' => $uniq_bb['name'], 'val' => $v, 'show_type' => 'txt'];
//                }
//            }
//            $fieldsMap[] = ['name' => '申请时间', 'val' => date('Y-m-d H:i:s', $item['created_at']), 'show_type' => 'txt'];
//            $item['fields_arr'] = $fieldsMap;
//            unset($item['post_data'], $item['bb_data']);
//        }
        //组合风控弹窗内容
        $bb_values = array_column($bb_data, 'uniq_bb_value');
        if (empty($bb_values)) {
            return rs('ok', 1, ['content' => '', 'status' => 4]);
        }
        $str = '<div style="color:#ff4906;">您所属报备唯一值:%s出现风控，请及时处理。</div>
  <div style="margin-top:6px;">报备出现风控后，如您不处理，有可能会被项目清退，并且造成损失。</div>
  <div style="margin-top:10px;color:#0d53ff;">立即申诉:</div>
  <div style="margin-top:6px;">1、请点击立即申诉，并提交推广证明 <br/>2、请确保您提交的信息真实可信，方便审核人员快速审核</div>
  <div style="margin-top:10px;color:#181c29;">放弃申诉:</div>
  <div style="margin-top:6px;">我们将不再提示风控通知，如果出现账号清退等情况，将有您自己承担后果</div>';
        $content = sprintf($str, implode('、', $bb_values));
        return rs('ok', 1, ['content' => $content, 'status' => 4]);
    }

    //风控报备数据
    private function _fk_bb($u, $d)
    {
        $u = glwb($u);
        $d = glwb($d);
        $uid = ints($u['uid']);
        $appid = ints($d['appid']);
        //获取所有报备数据
        $bb_data = M()->select('x_tg_bb_data', 'id,appid,bb_u_sj,bb_u_name,post_data,created_at,status,qr_image,fail_reason,bb_data,uniq_bb_zd,uniq_bb_value', ['uid' => $uid, 'is_deleted' => 0, 'appid' => $appid, 'status' => 4], 'id desc');
        if (empty($bb_data)) {
            return [];
        }
        //获取工单
        $bb_data = array_column($bb_data, null, 'id');
//        $fk_bbids = array_keys($bb_data);
//        if ($fk_bbids) {
//            RX('x_gongdan');
//            $fkss_lists = M()->select('x_gongdan', 'id,gl_yw_id,gl_yw_zt,gl_yw_data', ['uid' => $uid, 'lx' => x_gongdan::LX_BBFK_SHENSHU, 'gl_yw_id' => $fk_bbids, 'zt' => [1,2]], 'id desc');
//            if (!empty($fkss_lists)) {
//                foreach ($fkss_lists as $v) {
//                    if (isset($bb_data[$v['gl_yw_id']])) {
//                        unset($bb_data[$v['gl_yw_id']]);
//                    }
//                }
//            }
//        }
        return $bb_data;
    }

    //放弃申诉
    public function give_up_appeal($u, $d)
    {
        $u = glwb($u);
        $d = glwb($d);
        $uid = ints($u['uid']);
        $appid = ints($d['appid']);
        // 更新
        $check = [
            ['intzs', $uid, '抱歉，用户登录已过期!'],
            ['intzs', $appid, '抱歉，请先选择项目!'],
        ];
        $ck = check($check);
        if ($ck !== true) {
            return rs($ck);
        }
        $app = M()->get('x_tg_app', 'id,name', ['id' => $appid]);
        if (empty($app)) {
            return rs('推广对象不存在');
        }

        $res = m_tguser::update_or_insert_tips($uid, $appid, 'fk_popup', 2);
        if ($res) {
            return rs('放弃申诉成功', 1);
        }
        return rs('操作失败');
    }


    //更新x_tg_user_tips has_new_bb_fk_popup 数据
    public function gx_user_tips()
    {
        //查询用户报备
        $fk_bb_data = M()->select('x_tg_bb_data', 'appid,uid', ['status' => 4, 'is_deleted' => 0]);
        if (empty($fk_bb_data)) {
            return rs('暂无带处理数据', 1);
        }
        $updates = [];
        foreach ($fk_bb_data as $item) {
            $updates[] = ['uid' => $item['uid'], 'appid' => $item['appid'], 'has_new_bb_fk_popup' => 1];
        }
        if (empty($updates)) {
            return rs('无需更新', 1);
        }
        //更新x_tg_user_tips
        $res = M()->insertsOrUpdates('x_tg_user_tips', $updates, 1);
        if ($res) {
            return rs('更新用户风控tips状态成功', 1, $res);
        }
        return rs('更新用户风控tips状态失败');
    }

    /**
     * 检查是否有报备权限
     * @param $appid
     * @param $u
     * @return array|false|mixed|string|string[]
     */
    public function check_has_app_bb_qx($appid, $u)
    {
        $appid = ints($appid);
        $u = glwb($u);
        $uid = ints($u['uid'] ?? 0);
        // 参数校验
        $check = [
            ['intzs', $uid, '抱歉，用户登录已过期'],
            ['intzs', $appid, '抱歉，请先选择项目!'],
        ];
        $ck = check($check);
        if ($ck !== true) {
            return rs($ck);
        }

        $app = M()->get('x_tg_app', 'id,name,image,extra_data,status', ['id' => $appid]);
        if (!$app) {
            return rs('抱歉，当前项目不存在或已下架!');
        }

        //检查报备app uid是否在黑名单里
        if (M()->has('x_tg_app_black_list', ['appid' => $app['id'], 'tg_uid' => $uid])) {
            return rs('抱歉，您暂无报备此项目的权限，无法邀请团队报备!');
        }

        $extra_data = j2a($app['extra_data']);
        // 项目下架后是否允许报备
        if ($app['status'] == 2 && (!issetx('down_allow_bb', $extra_data) || $extra_data['down_allow_bb'] != 1)) {
            return rs('抱歉，当前项目已下架，暂时无法报备!');
        }

        // 项目上架后禁止报备
        if ($app['status'] == 1 && issetx('up_forbid_bb', $extra_data) && $extra_data['up_forbid_bb'] == 1) {
            $forbid_reason = '抱歉，' . ($extra_data['jzbb_reason'] ?? '当前项目禁止报备!');
            if (
                (!issetx('jzbb_s_date', $extra_data) && !issetx('jzbb_e_date', $extra_data)) ||
                (!issetx('jzbb_s_date', $extra_data) && issetx('jzbb_e_date', $extra_data) && time() <= strtotime($extra_data['jzbb_e_date'])) ||
                (issetx('jzbb_s_date', $extra_data) && !issetx('jzbb_e_date', $extra_data) && time() >= strtotime($extra_data['jzbb_s_date'])) ||
                (issetx('jzbb_s_date', $extra_data) && issetx('jzbb_e_date', $extra_data) && time() >= strtotime($extra_data['jzbb_s_date']) && time() <= strtotime($extra_data['jzbb_e_date']))
            ) {
                return rs($forbid_reason);
            }
        }

        if ($u && $u['is_sm'] == 2 && $u['is_wg'] == 1) {
            return rs('抱歉，请先完成实名认证!', -1, ['need_smrz' => 1]);
        }

        if ($u && $u['is_sm'] == 2 && $u['is_wg'] == 0 && time() - $u['created_at'] <= 60) {
            return rs('抱歉，请先完成实名认证!', -1, ['need_smrz' => 1]);
        }

        return rs('ok', 1, ['app' => $app]);
    }
}